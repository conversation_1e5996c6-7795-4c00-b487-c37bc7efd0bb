[tool.poetry]
name = "auth-service"
version = "0.1.0"
description = "Authentication Microservice"
authors = ["<PERSON><PERSON><PERSON><PERSON>g <EMAIL>"]

[tool.poetry.dependencies]
python = "^3.11"
# gRPC
grpcio = "^1.71.0"
grpcio-tools = "^1.71.0"
# Authentication and security
python-jose = {extras = ["cryptography"], version = "^3.4.0"}
passlib = {extras = ["bcrypt"], version = "^1.7.4"}
# Data validation and settings
pydantic = "^2.5.3"
pydantic-settings = "^2.1.0"
# Database
sqlalchemy = "^2.0.23"
alembic = "^1.13.1"
psycopg2-binary = "^2.9.9"
# Redis
redis = "^5.0.1"
# HTTP client for OAuth
httpx = "^0.25.2"
# Google Cloud
google-cloud-secret-manager = "^2.18.1"
google-auth = "^2.25.2"
# Utilities
python-dotenv = "^1.0.0"
# Logging and monitoring
structlog = "^23.2.0"
prometheus-client = "^0.19.0"

[tool.poetry.group.dev.dependencies]
pytest = "^8.2"
pytest-cov = "^5.0.0"
pytest-asyncio = "^0.26.0"
black = "^24.4.2"
isort = "^5.13.2"
mypy = "^1.10.0"
pylint = "^3.0.3"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 100
target-version = ["py311"]
include = '\.pyi?$'

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 100

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
check_untyped_defs = true

[tool.pylint.messages_control]
disable = ["C0111", "C0103", "C0330", "C0326"]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
addopts = "-v --cov=app --cov-report=term-missing"
asyncio_mode = "auto"