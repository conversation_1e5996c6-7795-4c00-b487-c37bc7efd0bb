"""Remove mcp_id from OAuth credentials and update composite keys

Revision ID: c2d3e4f5g6h7
Revises: b1c2d3e4f5g6
Create Date: 2025-06-17 12:00:00.000000

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = "c2d3e4f5g6h7"
down_revision: Union[str, None] = "b1c2d3e4f5g6"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Remove mcp_id from OAuth credentials and update composite keys."""

    # Step 1: Drop the entire table to avoid migration complexity
    op.drop_table("oauth_credentials")

    # Step 2: Recreate table with new schema (without mcp_id)
    op.create_table(
        "oauth_credentials",
        sa.Column("id", sa.String(), nullable=False),
        sa.Column("user_id", sa.String(), nullable=False),
        sa.Column("tool_name", sa.String(), nullable=False),
        sa.Column("provider", sa.String(), nullable=False, server_default="google"),
        sa.Column("composite_key", sa.String(), nullable=False),
        sa.Column("secret_reference", sa.String(), nullable=False),
        sa.Column("scopes", sa.Text(), nullable=True),
        sa.Column("created_at", sa.DateTime(), server_default=sa.text("now()"), nullable=False),
        sa.Column("updated_at", sa.DateTime(), server_default=sa.text("now()"), nullable=False),
        sa.Column("last_used_at", sa.DateTime(), server_default=sa.text("now()"), nullable=False),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("composite_key"),
    )

    # Step 3: Create new indexes (without mcp_id references)
    op.create_index("idx_oauth_composite_key", "oauth_credentials", ["composite_key"])
    op.create_index("idx_oauth_user_provider", "oauth_credentials", ["user_id", "provider"])
    op.create_index("idx_oauth_provider_tool", "oauth_credentials", ["provider", "tool_name"])
    op.create_index("idx_oauth_user_tool", "oauth_credentials", ["user_id", "tool_name"])
    op.create_index("idx_oauth_last_used", "oauth_credentials", ["last_used_at"])
    op.create_index("ix_oauth_credentials_user_id", "oauth_credentials", ["user_id"])
    op.create_index("ix_oauth_credentials_tool_name", "oauth_credentials", ["tool_name"])
    op.create_index("ix_oauth_credentials_provider", "oauth_credentials", ["provider"])


def downgrade() -> None:
    """Restore mcp_id column and revert composite keys."""

    # Step 1: Drop the new table
    op.drop_table("oauth_credentials")

    # Step 2: Recreate table with old schema (with mcp_id)
    op.create_table(
        "oauth_credentials",
        sa.Column("id", sa.String(), nullable=False),
        sa.Column("user_id", sa.String(), nullable=False),
        sa.Column("mcp_id", sa.String(), nullable=False),
        sa.Column("tool_name", sa.String(), nullable=False),
        sa.Column("provider", sa.String(), nullable=False, server_default="google"),
        sa.Column("composite_key", sa.String(), nullable=False),
        sa.Column("secret_reference", sa.String(), nullable=False),
        sa.Column("scopes", sa.Text(), nullable=True),
        sa.Column("created_at", sa.DateTime(), server_default=sa.text("now()"), nullable=False),
        sa.Column("updated_at", sa.DateTime(), server_default=sa.text("now()"), nullable=False),
        sa.Column("last_used_at", sa.DateTime(), server_default=sa.text("now()"), nullable=False),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("composite_key"),
    )

    # Step 3: Recreate old indexes
    op.create_index("idx_oauth_composite_key", "oauth_credentials", ["composite_key"])
    op.create_index("idx_oauth_user_provider", "oauth_credentials", ["user_id", "provider"])
    op.create_index("idx_oauth_provider_tool", "oauth_credentials", ["provider", "tool_name"])
    op.create_index(
        "idx_oauth_user_mcp_tool", "oauth_credentials", ["user_id", "mcp_id", "tool_name"]
    )
    op.create_index("idx_oauth_last_used", "oauth_credentials", ["last_used_at"])
    op.create_index("ix_oauth_credentials_user_id", "oauth_credentials", ["user_id"])
    op.create_index("ix_oauth_credentials_mcp_id", "oauth_credentials", ["mcp_id"])
    op.create_index("ix_oauth_credentials_tool_name", "oauth_credentials", ["tool_name"])
    op.create_index("ix_oauth_credentials_provider", "oauth_credentials", ["provider"])
    op.create_index("idx_oauth_mcp_user", "oauth_credentials", ["mcp_id", "user_id"])
