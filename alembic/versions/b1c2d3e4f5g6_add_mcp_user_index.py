"""Add MCP user index for optimized lookups

Revision ID: b1c2d3e4f5g6
Revises: a69c46b190df
Create Date: 2025-06-11 23:20:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision: str = 'b1c2d3e4f5g6'
down_revision: Union[str, None] = 'a69c46b190df'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Add MCP user index for optimized credential lookups."""
    # Add new composite index for MCP-specific user credential lookups
    op.create_index(
        'idx_oauth_mcp_user',
        'oauth_credentials',
        ['mcp_id', 'user_id'],
        unique=False
    )


def downgrade() -> None:
    """Remove MCP user index."""
    # Drop the index
    op.drop_index('idx_oauth_mcp_user', table_name='oauth_credentials')
