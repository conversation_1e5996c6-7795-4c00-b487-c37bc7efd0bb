import enum
import json  # For loading/dumping JSON for encryption
from datetime import datetime, timed<PERSON>ta
from sqlalchemy import (
    create_engine,
    Column,
    Integer,
    String,
    DateTime,
    ForeignKey,
    JSON,
    Boolean,
    Text,
    UniqueConstraint,
)
from sqlalchemy.orm import relationship, sessionmaker, declarative_base
from sqlalchemy.dialects.postgresql import JSONB  # For PostgreSQL, otherwise use <PERSON>SO<PERSON>
from sqlalchemy.types import Enum as SAEnum
import uuid
from datetime import datetime, timezone
from sqlalchemy import Column, String, DateTime, Index, Text
from sqlalchemy.sql import func
from sqlalchemy.orm import declarative_base


Base = declarative_base()


# --- Enum for Connection Types ---
class ConnectionTypeEnum(enum.Enum):
    API_KEY = "api_key"
    OAUTH = "oauth"


# --- Integration Definition Model (Admin Managed) ---
class IntegrationDefinition(Base):
    __tablename__ = "integration_definitions"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()), index=True)
    logo = Column(String, nullable=True)
    name = Column(
        String, unique=True, nullable=False, index=True
    )  # e.g., "AWS Bedrock", "Google Calendar"
    # slug = Column(
    #     String, unique=True, nullable=False, index=True
    # )  # e.g., "aws-bedrock", "google-calendar"
    description = Column(Text, nullable=True)
    connection_type = Column(SAEnum(ConnectionTypeEnum), nullable=False)

    # `schema_definition`'s structure depends on `connection_type`:
    # 1. For API_KEY: A list of field definitions for user input.
    #    e.g., [
    #        {"name": "api_key","required": True, "description": "Your API secret key"},
    #        {"name": "region","required": False, "description": "AWS Region"}
    #    ]
    # 2. For OAUTH2: A single JSON object with your application's OAuth client configuration.
    #    e.g., {
    #        "client_id_env_var": "GOOGLE_CLIENT_ID",
    #        "client_secret_env_var": "GOOGLE_CLIENT_SECRET",
    #        "auth_url": "https://accounts.google.com/o/oauth2/v2/auth",
    #        "token_url": "https://oauth2.googleapis.com/token",
    #        "scopes": ["https://www.googleapis.com/auth/calendar.readonly", "openid", "email"],
    #        "redirect_uri_path": "/oauth/google/callback", // Path on your app
    #        "user_info_url": "Optional URL to fetch user identity after auth"
    #    }
    schema_definition = Column(JSON, nullable=False)

    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    user_configs = relationship("OAuthCredential", back_populates="definition")

    def __repr__(self):
        return f"<IntegrationDefinition(name='{self.name}', type='{self.connection_type.value}')>"

# --- User Integration Configuration Model (Stores User's Actual Credentials/Tokens) ---
class OAuthCredential(Base):
    """
    OAuth credential storage model.

    Stores references to OAuth tokens in Google Secret Manager
    along with metadata for credential management.
    """

    __tablename__ = "oauth_credentials_integrations"

    # Primary key
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))

    # OAuth credential identifiers
    user_id = Column(String, nullable=False, index=True)

    integration_definition_id = Column(
        String, ForeignKey("integration_definitions.id"), nullable=False
    )
    
    # `secret_reference` stores a JSON string where sensitive values are encrypted.
    # For API_KEY type: e.g., '{"api_key": "encrypted_...", "region": "us-east-1"}'
    # For OAUTH2 type: e.g., '{"access_token": "encrypted_...", "refresh_token": "encrypted_...", "expires_at": 1678886400, "granted_scopes": ["scope1"]}'
    # Secret Manager reference
    secret_reference = Column(String, nullable=False)

    # OAuth-specific metadata
    scopes = Column(Text, nullable=True)  # JSON string of granted scopes

    is_connected = Column(Boolean, default=False)  # True if successfully connected/authenticated
    is_update_available = Column(Boolean, default=False)
    # Timestamps
    created_at = Column(DateTime, default=func.now(), nullable=False)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), nullable=False)
    last_used_at = Column(DateTime, default=func.now(), nullable=False)

    
    definition = relationship("IntegrationDefinition", back_populates="user_configs")
    # Create indexes for faster lookups
    __table_args__ = (
        Index("idx_oauth_integrations_user_integration", "user_id", "integration_definition_id"),
        Index("idx_oauth_integrations_last_used", "last_used_at"),
        UniqueConstraint("user_id", "integration_definition_id", name="uq_oauth_integrations_user_integration"),
    )

    def __repr__(self) -> str:
        return f"<OAuthCredential user_id={self.user_id} integration_id={self.integration_definition_id}>"


