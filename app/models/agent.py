from datetime import datetime
import uuid
from sqlalchemy import (
    <PERSON>umn,
    Integer,
    String,
    DateTime,
    Enum,
    Foreign<PERSON>ey,
    JSON,
    Text,
    ARRAY,
    Boolean,
    Float,
)
from sqlalchemy.dialects.postgresql import ARRAY
from sqlalchemy.orm import declarative_base, relationship
from app.utils.constants.constants import (
    AgentOwnerTypeEnum,
    AgentCategoryEnum,
    AgentVisibilityEnum,
    AgentStatusEnum,
    AgentToneEnum,
    CategoryEnum,
)
from app.utils.constants.table_names import (
    AGENT_CONFIG_TABLE,
    AGENT_CAPABILITIES_TABLE,
    AGENT_VARIABLES_TABLE,
    AGENT_AVATARS_TABLE,
    # AGENT_TEMPLATES_TABLE, # Remove Template
)

Base = declarative_base()


class AgentCapabilities(Base):
    __tablename__ = AGENT_CAPABILITIES_TABLE

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    capabilities = Column(JSON, nullable=True)
    input_modes = Column(ARRAY(String), nullable=True)  # Text, Voice, Image, File upload
    output_modes = Column(ARRAY(String), nullable=True)  # Text, Voice, Image, File generation
    # Response model
    response_model = Column(ARRAY(String), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)


class AgentVariables(Base):
    __tablename__ = AGENT_VARIABLES_TABLE

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String(255), nullable=False)
    description = Column(String, nullable=True)
    type = Column(String, nullable=False)
    default_value = Column(String, nullable=True)
    agent_config_id = Column(
        String, ForeignKey(f"{AGENT_CONFIG_TABLE}.id"), nullable=False, index=True
    )

    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)


class AgentAvatar(Base):
    __tablename__ = AGENT_AVATARS_TABLE

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    url = Column(String, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)


class AgentModelConfig(Base):
    __tablename__ = "agent_model_configs"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    model_provider = Column(String, nullable=True)
    model_name = Column(String, nullable=True)
    temperature = Column(Float, nullable=True)
    max_tokens = Column(Integer, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # Relationship to AgentConfigVersion
    agent_config_versions = relationship("AgentConfigVersion", back_populates="model_config")


class AgentKnowledgeBase(Base):
    __tablename__ = "agent_knowledge_bases"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    files = Column(JSON, nullable=True)
    urls = Column(JSON, nullable=True)
    # You might need other related fields here, such as:
    # description = Column(Text, nullable=True)  # Description of the knowledge base
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # Relationship to AgentConfigVersion
    agent_config_versions = relationship("AgentConfigVersion", back_populates="knowledge_base")


class AgentConfig(Base):
    __tablename__ = AGENT_CONFIG_TABLE

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=False)
    avatar = Column(String(255), nullable=False)
    owner_id = Column(String, nullable=False)

    # Access Control
    user_ids = Column(ARRAY(String), nullable=True)
    owner_type = Column(Enum(AgentOwnerTypeEnum), nullable=False)

    # Link to the current active version
    current_version_id = Column(String, ForeignKey("agent_config_versions.id"), nullable=True)

    # Template Reference (REMOVED)
    agent_template_id = Column(String, ForeignKey("agent_marketplace_listings.id"), nullable=True)
    template_owner_id = Column(String, nullable=True)
    is_imported = Column(Boolean, default=False)
    is_updated = Column(Boolean, default=False)

    # Basic Configuration
    agent_category = Column(
        Enum(AgentCategoryEnum), nullable=False, default=AgentCategoryEnum.AI_AGENT
    )
    system_message = Column(Text, nullable=False)
    # Tools Configuration
    workflow_ids = Column(ARRAY(String), nullable=True)
    mcp_server_ids = Column(ARRAY(String), nullable=True)

    # Messaging Configuration
    agent_topic_type = Column(String, nullable=True)

    # new fields
    department = Column(String, nullable=True)
    organization_id = Column(String, nullable=True)

    # ruh_credentials = Column(Boolean, default=False)
    tone = Column(Enum(AgentToneEnum), nullable=True)

    is_bench_employee = Column(Boolean, default=False)
    is_changes_marketplace = Column(Boolean, nullable=True)
    is_a2a = Column(Boolean, default=False)
    is_customizable = Column(Boolean, default=False)

    # Capabilities reference
    capabilities_id = Column(String, ForeignKey(f"{AGENT_CAPABILITIES_TABLE}.id"), nullable=True)
    example_prompts = Column(ARRAY(String), nullable=True)
    category = Column(Enum(CategoryEnum), nullable=True, default=CategoryEnum.GENERAL)

    # Rating and usage tracking
    use_count = Column(Integer, default=0)
    average_rating = Column(Float, default=0.0)

    # Metadata
    visibility = Column(
        Enum(AgentVisibilityEnum), nullable=True, default=AgentVisibilityEnum.PRIVATE
    )

    # --- NEW: SQLAlchemy relationship to AgentVariables ---
    variables = relationship(
        "AgentVariables",
        foreign_keys="[AgentVariables.agent_config_id]",
        backref="agent_config",
        cascade="all, delete-orphan",
    )
    tags = Column(ARRAY(String), nullable=True)
    status = Column(Enum(AgentStatusEnum), nullable=False, default=AgentStatusEnum.ACTIVE)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # Relationship to all its versions
    versions = relationship(
        "AgentConfigVersion",
        back_populates="agent_config",
        cascade="all, delete-orphan",
        foreign_keys="AgentConfigVersion.agent_config_id",
    )
    # Relationship to the current active version
    current_version = relationship(
        "AgentConfigVersion", foreign_keys=[current_version_id], post_update=True
    )
    # Relationship to its listings on the marketplace
    marketplace_listings = relationship(
        "AgentMarketplaceListing",
        back_populates="agent_config",
        foreign_keys="AgentMarketplaceListing.agent_config_id",
    )


class AgentConfigVersion(Base):
    __tablename__ = "agent_config_versions"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    agent_config_id = Column(String, ForeignKey(f"{AGENT_CONFIG_TABLE}.id"), nullable=False)
    model_config_id = Column(String, ForeignKey("agent_model_configs.id"), nullable=True)
    knowledge_base_id = Column(String, ForeignKey("agent_knowledge_bases.id"), nullable=True)

    version_number = Column(String(50), nullable=False, default="1.0.0")
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=False)
    avatar = Column(String(255), nullable=False)

    # Basic Configuration
    agent_category = Column(
        Enum(AgentCategoryEnum), nullable=False, default=AgentCategoryEnum.AI_AGENT
    )
    system_message = Column(Text, nullable=False)

    # Tools Configuration
    workflow_ids = Column(ARRAY(String), nullable=True)
    mcp_server_ids = Column(ARRAY(String), nullable=True)

    # Messaging Configuration
    agent_topic_type = Column(String, nullable=True)

    # new fields
    department = Column(String, nullable=True)
    organization_id = Column(String, nullable=True)

    # ruh_credentials = Column(Boolean, default=False)
    tone = Column(Enum(AgentToneEnum), nullable=True)

    is_bench_employee = Column(Boolean, default=False)
    is_changes_marketplace = Column(Boolean, nullable=True)
    is_a2a = Column(Boolean, default=False)
    is_customizable = Column(Boolean, default=False)

    # Capabilities reference
    capabilities_id = Column(String, ForeignKey(f"{AGENT_CAPABILITIES_TABLE}.id"), nullable=True)
    example_prompts = Column(ARRAY(String), nullable=True)
    category = Column(Enum(CategoryEnum), nullable=True, default=CategoryEnum.GENERAL)

    # Metadata
    tags = Column(ARRAY(String), nullable=True)
    status = Column(Enum(AgentStatusEnum), nullable=False, default=AgentStatusEnum.ACTIVE)
    version_notes = Column(Text, nullable=True)  # Add version_notes
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    agent_config = relationship(
        "AgentConfig", back_populates="versions", foreign_keys=[agent_config_id]
    )
    model_config = relationship(
        "AgentModelConfig", back_populates="agent_config_versions", foreign_keys=[model_config_id]
    )
    knowledge_base = relationship(
        "AgentKnowledgeBase",
        back_populates="agent_config_versions",
        foreign_keys=[knowledge_base_id],
    )

    marketplace_listings = relationship(
        "AgentMarketplaceListing", back_populates="agent_config_version"
    )


class AgentMarketplaceListing(Base):
    __tablename__ = "agent_marketplace_listings"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))

    # Link to the specific version being listed
    agent_config_version_id = Column(String, ForeignKey("agent_config_versions.id"), nullable=False)
    version_number = Column(String(50), nullable=False)  # Add version_number
    # Link back to the parent AgentConfig for easier querying if needed
    agent_config_id = Column(String, ForeignKey(f"{AGENT_CONFIG_TABLE}.id"), nullable=False)

    listed_by_user_id = Column(String, nullable=False)  # User who created this listing

    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    image_url = Column(String(255), nullable=True)  # Optional image for the listing

    use_count = Column(Integer, default=0)
    execution_count = Column(Integer, default=0)
    average_rating = Column(Float, default=0.0)

    visibility = Column(
        Enum(AgentVisibilityEnum), nullable=True, default=AgentVisibilityEnum.PUBLIC
    )
    status = Column(Enum(AgentStatusEnum), nullable=False, default=AgentStatusEnum.ACTIVE)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    agent_config_version = relationship("AgentConfigVersion", back_populates="marketplace_listings")
    agent_config = relationship(
        "AgentConfig", back_populates="marketplace_listings", foreign_keys=[agent_config_id]
    )

    # add tags column for AgentMarketplaceListing
    tags = Column(ARRAY(String), nullable=True, default=list)

    def __repr__(self):
        return f"<AgentMarketplaceListing id={self.id} title='{self.title}' version_id='{self.agent_config_version_id}'>"
