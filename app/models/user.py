from datetime import datetime
from sqlalchemy import Column, ForeignKey, String, DateTime, Boolean, Integer, JSON, Enum as SQLAlchemyEnum
from sqlalchemy.orm import declarative_base , relationship
from enum import Enum
from app.core.config import settings

Base = declarative_base()


class UserRole(str, Enum):
    USER = "user"


class User(Base): 
    __tablename__ = "users"

    id = Column(String, primary_key=True)
    email = Column(String, unique=True, nullable=False, index=True)
    full_name = Column(String, nullable=False)
    hashed_password = Column(String, nullable=True)  # Nullable for OAuth users
    is_active = Column(Boolean, default=True)
    is_oauth_user = Column(Boolean, default=False)
    oauth_provider = Column(String, nullable=True)
    google_id = Column(String, unique=True, nullable=True)  # New column for Google ID
    role = Column(SQLAlchemyEnum(UserRole), default=UserRole.USER, nullable=False)  # Enum column
    otp = Column(String, nullable=True)  # Stores the OTP
    otp_timestamp = Column(DateTime, nullable=True)  # When OTP was generated
    otp_attempts = Column(Integer, default=0)  # Number of verification attempts
    is_email_verified = Column(Boolean, default=False)  # Verification status

    # --- New Fields ---
    company = Column(String, nullable=True)
    department = Column(String, nullable=True) # Stores the selected or specified department
    job_role = Column(String, nullable=True)   # Stores the selected or specified role/job title
    
    # --- New Field ---
    stripe_customer_id = Column(String, unique=True, nullable=True, index=True) # Stripe Customer ID

    fcm_token = Column(String, nullable=True)
    
    # New fields for GitHub OAuth
    github_access_token = Column(String, nullable=True)
    
    default_organization = Column(String, nullable=True)

    
    is_first_login = Column(Boolean, default=True)
    phone_number = Column(String, nullable=True)
    profile_image = Column(String, nullable=False , default=settings.DEFAULT_PROFILE_PICTURE)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f"<User {self.email}>" 
    