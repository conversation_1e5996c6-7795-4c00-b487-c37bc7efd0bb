"""
Authentication Service Configuration

This module defines the configuration settings for the authentication service,
including database connections, OAuth providers, security settings, and
external service integrations.
"""

from typing import Optional

from pydantic import Field, SecretStr
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """Authentication service configuration settings."""

    model_config = SettingsConfigDict(
        env_file=".env", env_file_encoding="utf-8", case_sensitive=True, extra="ignore"
    )

    # === Application Settings ===
    APP_NAME: str = Field(default="Authentication Service", description="Application name")
    APP_VERSION: str = Field(default="1.0.0", description="Application version")
    DEBUG: bool = Field(default=False, description="Debug mode")
    ENVIRONMENT: str = Field(
        default="development", description="Environment (development/staging/production)"
    )

    # === gRPC Server Settings ===
    GRPC_PORT: int = Field(default=50054, description="gRPC server port")

    # === Database Settings ===
    DB_HOST: str = Field(default="localhost", description="Database host")
    DB_PORT: int = Field(default=5432, description="Database port")
    DB_USER: str = Field(default="auth_service", description="Database username")
    DB_PASSWORD: SecretStr = Field(default="password", description="Database password")
    DB_NAME: str = Field(default="authentication", description="Database name")
    DB_POOL_SIZE: int = Field(default=50, description="Database connection pool size")
    DB_MAX_OVERFLOW: int = Field(default=100, description="Database max overflow connections")

    # === Redis Settings ===
    REDIS_HOST: str = Field(default="localhost", description="Redis host")
    REDIS_PORT: int = Field(default=6379, description="Redis port")
    REDIS_PASSWORD: Optional[SecretStr] = Field(default=None, description="Redis password")
    REDIS_DB: int = Field(default=0, description="Redis database number")
    REDIS_POOL_SIZE: int = Field(default=20, description="Redis connection pool size")

    # === Google Secret Manager Settings ===
    GOOGLE_PROJECT_ID: Optional[str] = Field(default=None, description="Google Cloud project ID")
    GOOGLE_APPLICATION_CREDENTIALS: Optional[str] = Field(
        default=None, description="Path to Google service account credentials"
    )
    SECRET_MANAGER_ENABLED: bool = Field(default=True, description="Enable Google Secret Manager")

    # === Security Settings ===
    JWT_SECRET_KEY: SecretStr = Field(
        default="dev-secret-key", description="JWT secret key for token validation"
    )
    JWT_ALGORITHM: str = Field(default="HS256", description="JWT algorithm")
    JWT_EXPIRATION_MINUTES: int = Field(default=60, description="JWT token expiration in minutes")

    SERVER_AUTH_KEY: SecretStr = Field(
        default="dev-server-key", description="Server-to-server authentication key"
    )

    # OAuth state management
    OAUTH_STATE_EXPIRY_SECONDS: int = Field(
        default=900, description="OAuth state expiry (15 minutes)"
    )

    # Rate limiting
    RATE_LIMIT_REQUESTS: int = Field(default=100, description="Rate limit requests per minute")
    RATE_LIMIT_WINDOW: int = Field(default=60, description="Rate limit window in seconds")

    # === OAuth Provider Settings ===

    # Google OAuth
    GOOGLE_CLIENT_ID: Optional[str] = Field(default=None, description="Google OAuth client ID")
    GOOGLE_CLIENT_SECRET: Optional[SecretStr] = Field(
        default=None, description="Google OAuth client secret"
    )
    GOOGLE_REDIRECT_URI: Optional[str] = Field(
        default=None, description="Google OAuth redirect URI"
    )

    # Microsoft OAuth
    MICROSOFT_CLIENT_ID: Optional[str] = Field(
        default=None, description="Microsoft OAuth client ID"
    )
    MICROSOFT_CLIENT_SECRET: Optional[SecretStr] = Field(
        default=None, description="Microsoft OAuth client secret"
    )
    MICROSOFT_REDIRECT_URI: Optional[str] = Field(
        default=None, description="Microsoft OAuth redirect URI"
    )
    MICROSOFT_TENANT_ID: Optional[str] = Field(default="common", description="Microsoft tenant ID")

    # GitHub OAuth
    GITHUB_CLIENT_ID: Optional[str] = Field(default=None, description="GitHub OAuth client ID")
    GITHUB_CLIENT_SECRET: Optional[SecretStr] = Field(
        default=None, description="GitHub OAuth client secret"
    )
    GITHUB_REDIRECT_URI: Optional[str] = Field(
        default=None, description="GitHub OAuth redirect URI"
    )

    # Slack OAuth
    SLACK_CLIENT_ID: Optional[str] = Field(default=None, description="Slack OAuth client ID")
    SLACK_CLIENT_SECRET: Optional[SecretStr] = Field(
        default=None, description="Slack OAuth client secret"
    )
    SLACK_REDIRECT_URI: Optional[str] = Field(default=None, description="Slack OAuth redirect URI")

    # Zoho OAuth
    ZOHO_CLIENT_ID: Optional[str] = Field(default=None, description="ZOHO OAuth client ID")
    ZOHO_CLIENT_SECRET: Optional[SecretStr] = Field(
        default=None, description="ZOHO OAuth client secret"
    )
    ZOHO_REDIRECT_URI: Optional[str] = Field(default=None, description="ZOHO OAuth redirect URI")

    # Custom OAuth providers (JSON string)
    CUSTOM_OAUTH_PROVIDERS: Optional[str] = Field(
        default=None, description="Custom OAuth providers configuration (JSON)"
    )

    # === API Gateway Integration ===
    API_GATEWAY_URL: Optional[str] = Field(default=None, description="API Gateway base URL")
    API_GATEWAY_AUTH_KEY: Optional[SecretStr] = Field(
        default=None, description="API Gateway authentication key"
    )

    # === Monitoring & Logging ===
    LOG_LEVEL: str = Field(default="INFO", description="Logging level")
    LOG_FORMAT: str = Field(default="json", description="Log format (json/text)")

    # Metrics
    METRICS_ENABLED: bool = Field(default=True, description="Enable metrics collection")
    METRICS_PORT: int = Field(default=9090, description="Metrics server port")

    # Health checks
    HEALTH_CHECK_INTERVAL: int = Field(default=30, description="Health check interval in seconds")

    # === Performance Settings ===
    REQUEST_TIMEOUT: int = Field(default=30, description="Request timeout in seconds")

    # Caching
    CACHE_TTL_SECONDS: int = Field(default=300, description="Default cache TTL in seconds")
    CREDENTIAL_CACHE_TTL: int = Field(default=600, description="Credential cache TTL in seconds")

    # Jira integration
    JIRA_CLIENT_ID: Optional[str] = Field(default=None, description="Jira OAuth client ID")
    JIRA_CLIENT_SECRET: Optional[SecretStr] = Field(
        default=None, description="Jira OAuth client secret"
    )
    JIRA_REDIRECT_URI: Optional[str] = Field(default=None, description="Jira OAuth redirect URI")

    # === Computed Properties ===

    @property
    def database_url(self) -> str:
        """Build PostgreSQL connection string."""
        password = self.DB_PASSWORD.get_secret_value() if self.DB_PASSWORD else ""
        return (
            f"postgresql://{self.DB_USER}:{password}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}"
        )

    @property
    def redis_url(self) -> str:
        """Build Redis connection string."""
        password = self.REDIS_PASSWORD.get_secret_value() if self.REDIS_PASSWORD else ""
        auth_part = f":{password}@" if password else ""
        return f"redis://{auth_part}{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"

    @property
    def is_production(self) -> bool:
        """Check if running in production environment."""
        return self.ENVIRONMENT.lower() == "production"

    @property
    def is_development(self) -> bool:
        """Check if running in development environment."""
        return self.ENVIRONMENT.lower() == "development"


# Global settings instance
settings = Settings()
