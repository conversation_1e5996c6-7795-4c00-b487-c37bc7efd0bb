"""
OAuth Service

This service provides OAuth functionality for multiple providers
with dynamic scope resolution and provider-specific handling.
"""

import json
import logging
import secrets
import uuid
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Tuple
from urllib.parse import urlencode

import httpx
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session

from app.core.config import settings
from app.core.oauth_providers import (
    OAuthProvider,
    oauth_provider_manager,
    ZohoMultiDCConfig,
    ZohoDataCenter,
)
from app.models.integrations import ConnectionTypeEnum, IntegrationDefinition, OAuthCredential
from app.utils.redis_service import redis_service

from app.utils.GSM.google_secret_manager import EncryptionManager
logger = logging.getLogger(__name__)


class OAuthService:
    """Generalized OAuth service for multiple providers."""

    def __init__(self):
        """Initialize OAuth service with provider configurations."""
        self.secret_manager = EncryptionManager()
        self.redis_service = redis_service

        # Rate limiting configuration
        self.rate_limit_requests = 10  # Max requests per user per minute
        self.rate_limit_window = 60  # Window in seconds

    def _initialize_providers(self):
        """Initialize OAuth provider configurations from settings."""

        # Google OAuth
        if settings.GOOGLE_CLIENT_ID and settings.GOOGLE_CLIENT_SECRET:
            oauth_provider_manager.update_provider_credentials(
                OAuthProvider.GOOGLE,
                settings.GOOGLE_CLIENT_ID,
                settings.GOOGLE_CLIENT_SECRET.get_secret_value(),
                settings.GOOGLE_REDIRECT_URI or "",
            )

        # Microsoft OAuth
        if settings.MICROSOFT_CLIENT_ID and settings.MICROSOFT_CLIENT_SECRET:
            oauth_provider_manager.update_provider_credentials(
                OAuthProvider.MICROSOFT,
                settings.MICROSOFT_CLIENT_ID,
                settings.MICROSOFT_CLIENT_SECRET.get_secret_value(),
                settings.MICROSOFT_REDIRECT_URI or "",
            )

        # GitHub OAuth
        if settings.GITHUB_CLIENT_ID and settings.GITHUB_CLIENT_SECRET:
            oauth_provider_manager.update_provider_credentials(
                OAuthProvider.GITHUB,
                settings.GITHUB_CLIENT_ID,
                settings.GITHUB_CLIENT_SECRET.get_secret_value(),
                settings.GITHUB_REDIRECT_URI or "",
            )

        # Slack OAuth
        if settings.SLACK_CLIENT_ID and settings.SLACK_CLIENT_SECRET:
            oauth_provider_manager.update_provider_credentials(
                OAuthProvider.SLACK,
                settings.SLACK_CLIENT_ID,
                settings.SLACK_CLIENT_SECRET.get_secret_value(),
                settings.SLACK_REDIRECT_URI or "",
            )

        # Jira OAuth
        if settings.JIRA_CLIENT_ID and settings.JIRA_CLIENT_SECRET:
            oauth_provider_manager.update_provider_credentials(
                OAuthProvider.JIRA,
                settings.JIRA_CLIENT_ID,
                settings.JIRA_CLIENT_SECRET.get_secret_value(),
                settings.JIRA_REDIRECT_URI or "",
            )

        # Zoho OAuth
        if settings.ZOHO_CLIENT_ID and settings.ZOHO_CLIENT_SECRET:
            oauth_provider_manager.update_provider_credentials(
                OAuthProvider.ZOHO,
                settings.ZOHO_CLIENT_ID,
                settings.ZOHO_CLIENT_SECRET.get_secret_value(),
                settings.ZOHO_REDIRECT_URI or "",
            )

        # Load custom providers
        if settings.CUSTOM_OAUTH_PROVIDERS:
            oauth_provider_manager.load_custom_providers_from_json(settings.CUSTOM_OAUTH_PROVIDERS)

    def _check_rate_limit(self, user_id: str) -> bool:
        """
        Check if user has exceeded OAuth initiation rate limit.

        Args:
            user_id: User ID to check

        Returns:
            True if within rate limit, False if exceeded
        """
        rate_limit_key = f"oauth_rate_limit:{user_id}"

        # Get current request count
        current_count = self.redis_service.get_data_from_redis(rate_limit_key)

        if current_count is None:
            # First request in window
            self.redis_service.set_data_in_redis_with_ttl(
                rate_limit_key, self.rate_limit_window, "1"
            )
            return True

        try:
            count = int(current_count)
            if count >= self.rate_limit_requests:
                logger.warning(f"Rate limit exceeded for user {user_id}: {count} requests")
                return False

            # Increment counter (Redis INCR would be better but using current interface)
            self.redis_service.set_data_in_redis_with_ttl(
                rate_limit_key, self.rate_limit_window, str(count + 1)
            )
            return True

        except ValueError:
            # Invalid count, reset
            self.redis_service.set_data_in_redis_with_ttl(
                rate_limit_key, self.rate_limit_window, "1"
            )
            return True

    def generate_authorization_url(
        self,
        user_id: str,
        tool_name: str,
        provider: OAuthProvider,
        custom_scopes: Optional[List[str]] = None,
        custom_redirect_uri: Optional[str] = None,
    ) -> Tuple[str, str]:
        """
        Generate OAuth authorization URL and state token with rate limiting and validation.

        Args:
            user_id: User ID
            tool_name: Tool name
            provider: OAuth provider
            custom_scopes: Custom scopes (optional)
            custom_redirect_uri: Custom redirect URI (optional)

        Returns:
            Tuple of (authorization_url, state_token)

        Raises:
            ValueError: If parameters are invalid or rate limit exceeded
            RuntimeError: If state storage fails
        """
        # Validate parameters
        self._validate_oauth_parameters(user_id, tool_name, provider)

        # Check rate limiting
        if not self._check_rate_limit(user_id):
            raise ValueError(
                f"Rate limit exceeded for user {user_id}. Max {self.rate_limit_requests} requests per minute."
            )

        # Get provider configuration - this is a placeholder for legacy flow
        # In practice, this method should be deprecated in favor of integration-based flow
        from app.core.oauth_providers import oauth_provider_manager

        provider_config = oauth_provider_manager.get_provider_config(provider)

        # Determine scopes
        scopes = custom_scopes if custom_scopes else provider_config.default_scopes

        # Generate cryptographically secure state token (32+ bytes)
        state_token = secrets.token_urlsafe(32)

        print(f"scopes: {scopes}")
        print(f"state token: {state_token}")
        # Always use the configured provider redirect URI for OAuth
        oauth_redirect_uri = provider_config.redirect_uri

        # Handle Slack's dual-scope system
        slack_scopes = None
        if oauth_provider_manager.is_slack_provider(provider):
            slack_scopes = oauth_provider_manager.get_slack_scopes_separated(tool_name)
            logger.info(
                f"Slack scopes separated - Bot: {slack_scopes['bot_scopes']}, User: {slack_scopes['user_scopes']}"
            )

        # Store state data in Redis with comprehensive information
        state_data = {
            "user_id": user_id,
            "tool_name": tool_name,
            "provider": provider.value,
            "scopes": scopes,
            "slack_scopes": slack_scopes,  # Store separated scopes for Slack
            "redirect_uri": oauth_redirect_uri,  # This is for OAuth provider callback
            "custom_redirect_uri": custom_redirect_uri,  # This is for final user redirect
            "created_at": datetime.now(timezone.utc).isoformat(),
        }

        redis_key = f"oauth_state:{state_token}"
        success = self.redis_service.set_data_in_redis_with_ttl(
            hash_key=redis_key, ttl=settings.OAUTH_STATE_EXPIRY_SECONDS, data=json.dumps(state_data)
        )

        if not success:
            raise RuntimeError("Failed to store OAuth state in Redis")

        # Build authorization URL parameters
        auth_params = {
            "client_id": provider_config.client_id,
            "redirect_uri": oauth_redirect_uri,
            "state": state_token,
            "response_type": provider_config.response_type,
        }

        # Handle Slack's dual-scope system
        if oauth_provider_manager.is_slack_provider(provider) and slack_scopes:
            # For Slack, use separate scope and user_scope parameters
            if slack_scopes["bot_scopes"]:
                auth_params["scope"] = ",".join(slack_scopes["bot_scopes"])
            if slack_scopes["user_scopes"]:
                auth_params["user_scope"] = ",".join(slack_scopes["user_scopes"])
        else:
            # For other providers, use standard scope parameter
            auth_params["scope"] = " ".join(scopes)

        # Add provider-specific parameters
        if provider_config.access_type:
            auth_params["access_type"] = provider_config.access_type

        if provider_config.prompt:
            auth_params["prompt"] = provider_config.prompt

        # Add extra parameters
        auth_params.update(provider_config.extra_auth_params)

        # Build authorization URL
        authorization_url = f"{provider_config.auth_url}?{urlencode(auth_params)}"

        logger.info(
            f"Generated OAuth authorization URL for {provider.value} - User: {user_id}, Tool: {tool_name}"
        )
        logger.info(f"Authorization URL: {authorization_url}")

        return authorization_url, state_token

    async def exchange_code_for_tokens(
        self, code: str, state: str
    ) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """
        Exchange authorization code for OAuth tokens with enhanced validation.

        Args:
            code: Authorization code from OAuth provider
            state: State token from OAuth provider

        Returns:
            Tuple of (tokens, state_data)

        Raises:
            ValueError: If state is invalid, expired, or code exchange fails
            RuntimeError: If token exchange request fails
        """
        # Validate input parameters
        if not code or not code.strip():
            raise ValueError("Authorization code is required")

        if not state or not state.strip():
            raise ValueError("State parameter is required")

        # Retrieve and validate state data using constant-time comparison
        redis_key = f"oauth_state:{state}"
        state_data = self.redis_service.get_json_data_from_redis(redis_key)
        print(f"state_data: {state_data}")
        if not state_data:
            logger.warning(f"Invalid or expired OAuth state: {state[:8]}...")
            raise ValueError("Invalid or expired OAuth state")

        # Validate state data structure - handle both legacy and dynamic integration flows
        if "integration_id" in state_data:
            # Dynamic integration flow - this should not be processed by this method
            logger.error(
                f"Dynamic integration flow detected in legacy exchange_code_for_tokens method. State data: {state_data}"
            )
            logger.error(
                "This indicates a routing issue in HandleOAuthCallback - dynamic flows should use exchange_code_for_tokens_for_integration"
            )
            raise ValueError(
                "Invalid state data structure - dynamic integration flow detected in legacy method"
            )
        else:
            # Legacy flow
            required_fields = ["user_id", "tool_name", "provider", "scopes", "created_at"]
            for field in required_fields:
                if field not in state_data:
                    logger.error(f"Missing required field in legacy state data: {field}")
                    raise ValueError("Invalid state data structure")

        # Clean up state from Redis immediately after retrieval
        self.redis_service.delete_data_from_redis(redis_key)

        # Legacy flow - extract provider from state data
        provider = OAuthProvider(state_data["provider"])
        from app.core.oauth_providers import oauth_provider_manager

        provider_config = oauth_provider_manager.get_provider_config(provider)

        # Prepare token exchange request
        token_data = {
            "client_id": provider_config.client_id,
            "client_secret": provider_config.client_secret,
            "code": code,
            "grant_type": "authorization_code",
            "redirect_uri": provider_config.redirect_uri,
        }

        # Handle Zoho Multi-DC support
        if provider == OAuthProvider.ZOHO:
            tokens = await self._handle_zoho_multidc_token_exchange(
                code, provider_config, state_data
            )
        else:
            # Standard token exchange for other providers
            tokens = await self._standard_token_exchange(code, provider_config, provider)

        # Validate required token fields
        if "access_token" not in tokens:
            logger.error(f"Missing access_token in response from {provider.value}")
            raise RuntimeError("Invalid token response: missing access_token")

        logger.info(
            f"Successfully exchanged code for tokens - Provider: {provider.value}, User: {state_data['user_id']}"
        )

        return tokens, state_data

    async def _standard_token_exchange(
        self, code: str, provider_config, provider: OAuthProvider
    ) -> Dict[str, Any]:
        """Standard token exchange for non-Zoho providers."""
        token_data = {
            "client_id": provider_config.client_id,
            "client_secret": provider_config.client_secret,
            "code": code,
            "grant_type": "authorization_code",
            "redirect_uri": provider_config.redirect_uri,
        }

        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                headers = {
                    "Accept": "application/json",
                    "Content-Type": "application/x-www-form-urlencoded",
                }

                response = await client.post(
                    provider_config.token_url, data=token_data, headers=headers
                )

                if response.status_code != 200:
                    error_text = response.text
                    logger.error(
                        f"Token exchange failed for provider {provider.value}: {response.status_code} - {error_text}"
                    )

                    # Try to parse error response
                    try:
                        error_data = response.json()
                        error_description = error_data.get(
                            "error_description", error_data.get("error", "Unknown error")
                        )
                        raise RuntimeError(f"OAuth token exchange failed: {error_description}")
                    except (ValueError, KeyError):
                        raise RuntimeError(
                            f"OAuth token exchange failed with status {response.status_code}"
                        )

                return response.json()

        except httpx.TimeoutException:
            logger.error(f"Timeout during token exchange with {provider.value}")
            raise RuntimeError("Token exchange request timed out")
        except httpx.RequestError as e:
            logger.error(f"Request error during token exchange with {provider.value}: {e}")
            raise RuntimeError(f"Token exchange request failed: {str(e)}")

    async def _handle_zoho_multidc_token_exchange(
        self, code: str, provider_config, state_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Handle Zoho Multi-DC token exchange by trying all data centers."""
        logger.info("Starting Zoho Multi-DC token exchange")

        token_data = {
            "client_id": provider_config.client_id,
            "client_secret": provider_config.client_secret,
            "code": code,
            "grant_type": "authorization_code",
            "redirect_uri": provider_config.redirect_uri,
        }

        # List of data centers to try in order (most common first)
        data_centers_to_try = [
            ZohoDataCenter.US,
            ZohoDataCenter.IN,
            ZohoDataCenter.EU,
            ZohoDataCenter.AU,
            ZohoDataCenter.JP,
            ZohoDataCenter.CA,
            ZohoDataCenter.SA,
            ZohoDataCenter.UK,
        ]

        last_error = None

        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                headers = {
                    "Accept": "application/json",
                    "Content-Type": "application/x-www-form-urlencoded",
                }

                for dc in data_centers_to_try:
                    token_url = ZohoMultiDCConfig.get_token_url(dc)
                    logger.info(f"Trying Zoho {dc.value} data center: {token_url}")

                    try:
                        response = await client.post(token_url, data=token_data, headers=headers)

                        if response.status_code == 200:
                            tokens = response.json()

                            # Check if we got valid tokens
                            if "access_token" in tokens:
                                logger.info(
                                    f"Successfully authenticated with Zoho {dc.value} data center"
                                )

                                # Ensure api_domain is set for this data center
                                if "api_domain" not in tokens:
                                    tokens["api_domain"] = ZohoMultiDCConfig.API_DOMAINS[dc]
                                    logger.info(f"Added api_domain: {tokens['api_domain']}")

                                return tokens
                            else:
                                logger.warning(f"No access_token in response from {dc.value} DC")
                                continue

                        elif response.status_code == 400:
                            # Invalid grant - this DC doesn't have this user, try next
                            error_data = (
                                response.json()
                                if response.headers.get("content-type", "").startswith(
                                    "application/json"
                                )
                                else {}
                            )
                            if error_data.get("error") == "invalid_grant":
                                logger.info(f"User not found in {dc.value} DC, trying next...")
                                continue
                            else:
                                last_error = f"Error from {dc.value} DC: {error_data.get('error_description', response.text)}"
                                logger.warning(last_error)
                        else:
                            last_error = (
                                f"HTTP {response.status_code} from {dc.value} DC: {response.text}"
                            )
                            logger.warning(last_error)

                    except httpx.RequestError as e:
                        last_error = f"Network error with {dc.value} DC: {str(e)}"
                        logger.warning(last_error)
                        continue

                # If we get here, none of the data centers worked
                raise RuntimeError(
                    f"Zoho token exchange failed across all data centers. Last error: {last_error}"
                )

        except httpx.TimeoutException:
            logger.error("Timeout during Zoho token exchange")
            raise RuntimeError("Zoho token exchange request timed out")
        except httpx.RequestError as e:
            logger.error(f"Request error during Zoho token exchange: {e}")
            raise RuntimeError(f"Zoho token exchange request failed: {str(e)}")

    def store_oauth_credentials(
        self,
        db: Session,
        user_id: str,
        tool_name: str,
        provider: OAuthProvider,
        tokens: Dict[str, Any],
        scopes: List[str],
    ) -> Dict[str, Any]:
        """
        Store OAuth credentials in encrypted format in database.

        Args:
            db: Database session
            user_id: User ID
            tool_name: Tool name
            provider: OAuth provider
            tokens: Token dictionary from OAuth exchange
            scopes: Granted scopes

        Returns:
            Dictionary with operation status
        """
        try:
            # Create composite key (includes provider)
            composite_key = OAuthCredential.generate_composite_key(
                user_id, tool_name, provider.value
            )

            # Check if credential already exists
            existing_credential = (
                db.query(OAuthCredential)
                .filter(OAuthCredential.composite_key == composite_key)
                .first()
            )

            # Convert tokens to JSON string
            tokens_dict = {**tokens, "scopes": scopes}
            tokens_json_string = json.dumps(tokens_dict)
            logger.debug(f"[DEBUG -> Legacy tokens to JSON string] {tokens_json_string}")

            # Encrypt the JSON string
            try:
                encrypted_blob = self.secret_manager.encrypt(tokens_json_string, user_id)
            except ValueError as encryption_error:
                if "No encryption key found" in str(encryption_error):
                    logger.warning(f"No encryption key found for user {user_id}, creating one...")
                    secret_id = self.secret_manager._get_secret_name(user_id)
                    self.secret_manager.create_and_store_user_key(secret_id)
                    encrypted_blob = self.secret_manager.encrypt(tokens_json_string, user_id)
                else:
                    raise encryption_error

            if existing_credential:
                # Update existing credential
                existing_credential.secret_reference = encrypted_blob
                existing_credential.scopes = json.dumps(scopes)
                existing_credential.updated_at = datetime.now(timezone.utc)
                existing_credential.last_used_at = datetime.now(timezone.utc)

                logger.info(f"Updated OAuth credential for composite key: {composite_key}")
            else:
                # Create new credential
                new_credential = OAuthCredential(
                    id=str(uuid.uuid4()),
                    user_id=user_id,
                    tool_name=tool_name,
                    provider=provider.value,
                    composite_key=composite_key,
                    secret_reference=encrypted_blob,
                    scopes=json.dumps(scopes),
                )

                db.add(new_credential)
                logger.info(f"Created new OAuth credential for composite key: {composite_key}")

            db.commit()

            return {
                "success": True,
                "message": "OAuth credentials stored successfully",
                "composite_key": composite_key,
            }

        except IntegrityError as e:
            db.rollback()
            logger.error(f"Database integrity error storing OAuth credentials: {e}")
            return {"success": False, "message": "Database integrity error", "error": str(e)}
        except Exception as e:
            db.rollback()
            logger.error(f"Error storing OAuth credentials: {e}")
            return {
                "success": False,
                "message": "Failed to store OAuth credentials",
                "error": str(e),
            }

    def retrieve_oauth_credentials(
        self, db: Session, user_id: str, tool_name: str, provider: OAuthProvider
    ) -> Optional[Dict[str, Any]]:
        """
        Retrieve OAuth credentials from database and decrypt them.

        Args:
            db: Database session
            user_id: User ID
            tool_name: Tool name
            provider: OAuth provider

        Returns:
            OAuth credentials dictionary if found, None otherwise
        """
        try:
            # Create composite key
            composite_key = OAuthCredential.generate_composite_key(
                user_id, tool_name, provider.value
            )

            # Find credential in database
            credential = (
                db.query(OAuthCredential)
                .filter(OAuthCredential.composite_key == composite_key)
                .first()
            )

            if not credential:
                logger.warning(f"No OAuth credential found for composite key: {composite_key}")
                return None

            # Decrypt the stored encrypted blob
            try:
                decrypted_json_string = self.secret_manager.decrypt(
                    credential.secret_reference, user_id
                )
                tokens_dict = json.loads(decrypted_json_string)
                logger.debug(f"[DEBUG -> Legacy decrypted tokens] {tokens_dict}")
            except Exception as decrypt_error:
                logger.error(
                    f"Error decrypting OAuth credentials for user {user_id}: {decrypt_error}"
                )
                return None

            # Extract tokens and scopes from decrypted data
            tokens = {k: v for k, v in tokens_dict.items() if k != "scopes"}
            decrypted_scopes = tokens_dict.get("scopes", [])

            # Update last used timestamp
            credential.update_last_used()
            db.commit()

            logger.info(f"Retrieved OAuth credentials for composite key: {composite_key}")

            # Build base response
            response = {
                "user_id": user_id,
                "tool_name": tool_name,
                "provider": provider.value,
                "access_token": tokens.get("access_token"),
                "refresh_token": tokens.get("refresh_token"),
                "token_type": tokens.get("token_type", "Bearer"),
                "expires_in": tokens.get("expires_in"),
                "scope": " ".join(decrypted_scopes),
                "scopes": decrypted_scopes,
                "tokens": tokens,
            }

            # Add Slack-specific token fields if this is a Slack provider
            if oauth_provider_manager.is_slack_provider(provider):
                response = {
                    "user_id": user_id,
                    "tool_name": tool_name,
                    "provider": provider.value,
                    "access_token": "",
                    "refresh_token": "",
                    "token_type": "Bearer",
                    "expires_in": 0,
                    "scope": tokens.get("scope"),
                    "scopes": scopes,
                }
                # Bot token (main access_token)
                response["bot_token"] = tokens.get("access_token")
                response["bot_user_id"] = tokens.get("bot_user_id")

                # User token from authed_user
                authed_user = tokens.get("authed_user", {})
                if authed_user and authed_user.get("access_token"):
                    response["user_token"] = authed_user.get("access_token")
                    response["user_id_slack"] = authed_user.get("id")
                    response["user_scope"] = authed_user.get("scope", "")

                # Team/workspace information
                team = tokens.get("team", {})
                if team:
                    response["team_id"] = team.get("id")
                    response["team_name"] = team.get("name")

            return response

        except Exception as e:
            logger.error(f"Error retrieving OAuth credentials: {e}")
            return None

    def delete_oauth_credentials(
        self, db: Session, user_id: str, tool_name: str, provider: OAuthProvider
    ) -> bool:
        """
        Delete OAuth credentials from database.

        Args:
            db: Database session
            user_id: User ID
            tool_name: Tool name
            provider: OAuth provider

        Returns:
            True if successful, False otherwise
        """
        try:
            # Create composite key
            composite_key = OAuthCredential.generate_composite_key(
                user_id, tool_name, provider.value
            )

            # Find credential in database
            credential = (
                db.query(OAuthCredential)
                .filter(OAuthCredential.composite_key == composite_key)
                .first()
            )

            if not credential:
                logger.warning(f"No OAuth credential found for deletion: {composite_key}")
                return True  # Consider it successful if already deleted

            # Delete from database (encrypted data is stored in secret_reference field)
            db.delete(credential)
            db.commit()

            logger.info(f"Deleted OAuth credential: {composite_key}")

            return True

        except Exception as e:
            db.rollback()
            logger.error(f"Error deleting OAuth credentials: {e}")
            return False

    async def refresh_access_token(
        self, db: Session, user_id: str, tool_name: str, provider: OAuthProvider
    ) -> Optional[Dict[str, Any]]:
        """
        Refresh OAuth access token using refresh token.

        Args:
            db: Database session
            user_id: User ID
            tool_name: Tool name
            provider: OAuth provider

        Returns:
            Updated credentials if successful, None otherwise
        """
        try:
            # Get current credentials
            credentials = self.retrieve_oauth_credentials(db, user_id, tool_name, provider)

            if not credentials or not credentials.get("tokens", {}).get("refresh_token"):
                logger.warning(f"No refresh token available for {user_id}/{tool_name}")
                return None

            refresh_token = credentials["tokens"]["refresh_token"]

            # Get provider configuration
            from app.core.oauth_providers import oauth_provider_manager

            provider_config = oauth_provider_manager.get_provider_config(provider)

            # Prepare refresh token request
            token_data = {
                "grant_type": "refresh_token",
                "refresh_token": refresh_token,
                "client_id": provider_config.client_id,
                "client_secret": provider_config.client_secret,
            }

            # Add scope if available
            if credentials.get("scopes"):
                token_data["scope"] = " ".join(credentials["scopes"])

            headers = {
                "Content-Type": "application/x-www-form-urlencoded",
                "Accept": "application/json",
            }

            # Make token refresh request
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    provider_config.token_url,
                    data=token_data,
                    headers=headers,
                )

                if response.status_code != 200:
                    logger.error(
                        f"Token refresh failed with status {response.status_code}: {response.text}"
                    )
                    return None

                token_response = response.json()

                # Validate required token fields
                if "access_token" not in token_response:
                    logger.error(f"Missing access_token in refresh response from {provider.value}")
                    return None

                # Update existing tokens with new ones
                updated_tokens = credentials["tokens"].copy()
                updated_tokens["access_token"] = token_response["access_token"]

                # Update refresh token if provided
                if "refresh_token" in token_response:
                    updated_tokens["refresh_token"] = token_response["refresh_token"]

                # Update other token fields
                if "expires_in" in token_response:
                    updated_tokens["expires_in"] = token_response["expires_in"]
                if "token_type" in token_response:
                    updated_tokens["token_type"] = token_response["token_type"]
                if "scope" in token_response:
                    updated_tokens["scope"] = token_response["scope"]

                # Store updated credentials
                scopes = credentials.get("scopes", [])
                if "scope" in token_response:
                    scopes = token_response["scope"].split()

                store_result = self.store_oauth_credentials(
                    db, user_id, tool_name, provider, updated_tokens, scopes
                )

                if store_result.get("success"):
                    logger.info(
                        f"Successfully refreshed OAuth token for user {user_id}, tool {tool_name}"
                    )
                    return token_response
                else:
                    logger.error(f"Failed to store refreshed tokens: {store_result.get('message')}")
                    return None

        except httpx.TimeoutException:
            logger.error(f"Timeout during token refresh with {provider.value}")
            return None
        except httpx.RequestError as e:
            logger.error(f"Request error during token refresh with {provider.value}: {e}")
            return None
        except Exception as e:
            logger.error(f"Error refreshing access token: {e}")
            return None

    def generate_authorization_url_for_integration(
        self,
        db: Session,
        user_id: str,
        integration_id: str,
        custom_redirect_uri: Optional[str] = None,
    ) -> Tuple[str, str]:
        """
        Generate OAuth authorization URL using a dynamic IntegrationDefinition from the database.
        Client ID, Client Secret, and Redirect URI are always taken from environment variables.

        Args:
            db: Database session.
            user_id: The ID of the user initiating the flow.
            integration_id: The ID of the integration to use.
            custom_redirect_uri: An optional final redirect URI for the user after callback.

        Returns:
            A tuple containing the authorization URL and the state token.

        Raises:
            ValueError: If the integration is not found or not configured for OAuth.
        """
        # 1. Fetch the Integration Definition from the database
        integration_def = (
            db.query(IntegrationDefinition)
            .filter(IntegrationDefinition.id == integration_id)
            .first()
        )

        if not integration_def:
            raise ValueError(f"Integration with ID '{integration_id}' not found.")

        if integration_def.connection_type != ConnectionTypeEnum.OAUTH:
            raise ValueError(f"Integration '{integration_def.name}' is not an OAuth integration.")

        # 2. Extract OAuth configuration from the schema
        oauth_schema = integration_def.schema_definition

        # 3. Get OAuth credentials from environment variables using names from schema
        client_id_env_var = oauth_schema.get("client_id_env_var")
        client_secret_env_var = oauth_schema.get("client_secret_env_var")
        redirect_uri_env_var = oauth_schema.get("redirect_uri_env_var")

        print(f"[DEBUG] redirect_uri_env_var: {redirect_uri_env_var}")
        
        if not client_id_env_var:
            raise ValueError(
                f"Client ID environment variable not specified in schema for '{integration_def.name}'. "
                f"Please add 'client_id_env_var' to the schema definition."
            )

        if not client_secret_env_var:
            raise ValueError(
                f"Client Secret environment variable not specified in schema for '{integration_def.name}'. "
                f"Please add 'client_secret_env_var' to the schema definition."
            )

        if not redirect_uri_env_var:
            raise ValueError(
                f"Redirect URI environment variable not specified in schema for '{integration_def.name}'. "
                f"Please add 'redirect_uri_env_var' to the schema definition."
            )

        # Get actual values from environment variables
        client_id = getattr(settings, client_id_env_var, None)
        client_secret = getattr(settings, client_secret_env_var, None)
        redirect_uri = getattr(settings, redirect_uri_env_var, None)

        print(f"[DEBUG] redirect_uri: {redirect_uri}")
        if not client_id:
            raise ValueError(
                f"Client ID for '{integration_def.name}' not configured in environment. "
                f"Please set {client_id_env_var} in your environment variables."
            )

        if not client_secret:
            raise ValueError(
                f"Client Secret for '{integration_def.name}' not configured in environment. "
                f"Please set {client_secret_env_var} in your environment variables."
            )

        if not redirect_uri:
            raise ValueError(
                f"Redirect URI for '{integration_def.name}' not configured in environment. "
                f"Please set {redirect_uri_env_var} in your environment variables."
            )

        # 4. Generate state and store it in Redis
        state_token = secrets.token_urlsafe(32)
        state_data = {
            "user_id": user_id,
            "integration_id": integration_def.id,
            "provider_redirect_uri": redirect_uri,
            "custom_redirect_uri": custom_redirect_uri,
            "created_at": datetime.now(timezone.utc).isoformat(),
        }
        redis_key = f"oauth_state:{state_token}"
        self.redis_service.set_data_in_redis_with_ttl(
            redis_key, settings.OAUTH_STATE_EXPIRY_SECONDS, json.dumps(state_data)
        )

        print(f"[DEBUG] state_data: {state_data}")

        # 5. Build the authorization URL
        auth_params = {
            "client_id": client_id,
            "redirect_uri": redirect_uri,
            "scope": " ".join(oauth_schema["scopes"]),
            "state": state_token,
            "response_type": "code",
        }

        # Add extra parameters from schema if they exist
        if "extra_auth_params" in oauth_schema:
            auth_params.update(oauth_schema["extra_auth_params"])

        authorization_url = f"{oauth_schema['auth_url']}?{urlencode(auth_params)}"

        logger.info(f"Generated dynamic OAuth URL for {integration_def.name} for user {user_id}")

        return authorization_url, state_token

    async def exchange_code_for_tokens_for_integration(
        self, db: Session, code: str, state: str
    ) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """
        Exchange authorization code for OAuth tokens for a dynamic integration.
        Client ID and Client Secret are always taken from environment variables.

        Args:
            db: Database session.
            code: Authorization code from the OAuth provider.
            state: State token from the OAuth provider.

        Returns:
            A tuple containing the tokens and the original state data.
        """
        print(f"[DEBUG] exchange_code_for_tokens_for_integration : {code} state : {state}")
        # 1. Retrieve and validate state data from Redis
        redis_key = f"oauth_state:{state}"
        state_data_raw = self.redis_service.get_data_from_redis(redis_key)
        if not state_data_raw:
            raise ValueError("Invalid or expired OAuth state.")

        state_data = json.loads(state_data_raw)
        print(f"[DEBUG] state_data: {state_data}")
        self.redis_service.delete_data_from_redis(redis_key)

        # 2. Fetch the Integration Definition
        integration_id = state_data.get("integration_id")
        if not integration_id:
            raise ValueError("Integration ID not found in state data.")

        integration_def = (
            db.query(IntegrationDefinition)
            .filter(IntegrationDefinition.id == integration_id)
            .first()
        )
        if not integration_def:
            raise ValueError(f"Integration with ID '{integration_id}' not found.")

        # 3. Extract OAuth configuration from the schema
        oauth_schema = integration_def.schema_definition

        # 4. Get OAuth credentials from environment variables using names from schema
        client_id_env_var = oauth_schema.get("client_id_env_var")
        client_secret_env_var = oauth_schema.get("client_secret_env_var")

        if not client_id_env_var:
            raise ValueError(
                f"Client ID environment variable not specified in schema for '{integration_def.name}'. "
                f"Please add 'client_id_env_var' to the schema definition."
            )

        if not client_secret_env_var:
            raise ValueError(
                f"Client Secret environment variable not specified in schema for '{integration_def.name}'. "
                f"Please add 'client_secret_env_var' to the schema definition."
            )

        # Get actual values from environment variables
        client_id = getattr(settings, client_id_env_var, None)
        client_secret_setting = getattr(settings, client_secret_env_var, None)

        if not client_id:
            raise ValueError(
                f"Client ID for '{integration_def.name}' not configured in environment. "
                f"Please set {client_id_env_var} in your environment variables."
            )

        if not client_secret_setting:
            raise ValueError(
                f"Client Secret for '{integration_def.name}' not configured in environment. "
                f"Please set {client_secret_env_var} in your environment variables."
            )

        # Handle SecretStr type
        if hasattr(client_secret_setting, "get_secret_value"):
            client_secret = client_secret_setting.get_secret_value()
        else:
            client_secret = client_secret_setting

        token_data = {
            "client_id": client_id,
            "client_secret": client_secret,
            "code": code,
            "grant_type": "authorization_code",
            "redirect_uri": state_data["provider_redirect_uri"],
        }

        async with httpx.AsyncClient() as client:
            response = await client.post(
                oauth_schema["token_url"],
                data=token_data,
                headers={"Accept": "application/json"},
            )
            response.raise_for_status()  # Raise an exception for bad status codes
            tokens = response.json()

        return tokens, state_data

    def store_oauth_credentials_for_integration(
        self,
        db: Session,
        user_id: str,
        integration_id: str,
        tokens: Dict[str, Any],
        scopes: List[str],
    ) -> Dict[str, Any]:
        """
        Store OAuth credentials for a dynamic integration.

        Args:
            db: Database session.
            user_id: The ID of the user.
            integration_id: The ID of the integration definition.
            tokens: The OAuth tokens.
            scopes: The granted scopes.

        Returns:
            A dictionary with the operation status.
        """
        try:
            # Convert tokens to JSON string
            tokens_dict = {**tokens, "scopes": scopes}
            tokens_json_string = json.dumps(tokens_dict)
            logger.debug(f"[DEBUG -> Tokens to JSON string] {tokens_json_string}")

            # Encrypt the JSON string
            try:
                encrypted_blob = self.secret_manager.encrypt(tokens_json_string, user_id)
            except ValueError as encryption_error:
                if "No encryption key found" in str(encryption_error):
                    logger.warning(f"No encryption key found for user {user_id}, creating one...")
                    secret_id = self.secret_manager._get_secret_name(user_id)
                    self.secret_manager.create_and_store_user_key(secret_id)
                    encrypted_blob = self.secret_manager.encrypt(tokens_json_string, user_id)
                else:
                    raise encryption_error

            # Check if a credential already exists for this user and integration
            existing_credential = (
                db.query(OAuthCredential)
                .filter_by(user_id=user_id, integration_definition_id=integration_id)
                .first()
            )

            if existing_credential:
                # Update existing credential
                existing_credential.secret_reference = encrypted_blob
                existing_credential.scopes = json.dumps(scopes)
                existing_credential.is_connected = True
                existing_credential.updated_at = datetime.now(timezone.utc)
                logger.info(
                    f"Updated OAuth credential for user {user_id} and integration {integration_id}"
                )
            else:
                # Create new credential
                new_credential = OAuthCredential(
                    user_id=user_id,
                    integration_definition_id=integration_id,
                    secret_reference=encrypted_blob,
                    scopes=json.dumps(scopes),
                    is_connected=True,
                )
                db.add(new_credential)
                logger.info(
                    f"Created new OAuth credential for user {user_id} and integration {integration_id}"
                )

            db.commit()

            return {
                "success": True,
                "message": "OAuth credentials stored successfully",
            }
        except Exception as e:
            db.rollback()
            logger.error(f"Error storing OAuth credentials for integration: {e}")
            return {
                "success": False,
                "message": "Failed to store OAuth credentials",
                "error": str(e),
            }

    def retrieve_oauth_credentials_for_integration(
        self, db: Session, user_id: str, integration_id: str
    ) -> Optional[Dict[str, Any]]:
        """
        Retrieve OAuth credentials for a dynamic integration.

        Args:
            db: Database session.
            user_id: The ID of the user.
            integration_id: The ID of the integration definition.

        Returns:
            A dictionary with the credentials if found, otherwise None.
        """
        try:
            credential = (
                db.query(OAuthCredential)
                .filter_by(user_id=user_id, integration_definition_id=integration_id)
                .first()
            )

            if not credential:
                return None

            # Decrypt the stored encrypted blob
            try:
                decrypted_json_string = self.secret_manager.decrypt(
                    credential.secret_reference, user_id
                )
                tokens_dict = json.loads(decrypted_json_string)
                logger.debug(f"[DEBUG -> Decrypted tokens] {tokens_dict}")
            except Exception as decrypt_error:
                logger.error(
                    f"Error decrypting OAuth credentials for user {user_id}: {decrypt_error}"
                )
                return None

            # Extract tokens and scopes from decrypted data
            tokens = {k: v for k, v in tokens_dict.items() if k != "scopes"}
            decrypted_scopes = tokens_dict.get("scopes", [])

            credential.last_used_at = datetime.now(timezone.utc)
            db.commit()

            return {
                "user_id": user_id,
                "integration_id": integration_id,
                "access_token": tokens.get("access_token"),
                "refresh_token": tokens.get("refresh_token"),
                "token_type": tokens.get("token_type", "Bearer"),
                "expires_in": tokens.get("expires_in"),
                "scopes": decrypted_scopes,
                "tokens": tokens,
            }
        except Exception as e:
            logger.error(f"Error retrieving OAuth credentials for integration: {e}")
            return None

    def delete_oauth_credentials_for_integration(
        self, db: Session, user_id: str, integration_id: str
    ) -> bool:
        """
        Delete OAuth credentials for a dynamic integration.

        Args:
            db: Database session.
            user_id: The ID of the user.
            integration_id: The ID of the integration definition.

        Returns:
            True if successful, False otherwise.
        """
        try:
            credential = (
                db.query(OAuthCredential)
                .filter_by(user_id=user_id, integration_definition_id=integration_id)
                .first()
            )

            if not credential:
                logger.warning(
                    f"No OAuth credential found for deletion: user {user_id}, integration {integration_id}"
                )
                return True  # Consider it successful if already deleted

            # Delete from database
            db.delete(credential)
            db.commit()

            logger.info(
                f"Deleted OAuth credential for user {user_id} and integration {integration_id}"
            )
            return True

        except Exception as e:
            db.rollback()
            logger.error(f"Error deleting OAuth credentials for integration: {e}")
            return False

    async def refresh_access_token_for_integration(
        self, db: Session, user_id: str, integration_id: str
    ) -> Optional[Dict[str, Any]]:
        """
        Refresh OAuth access token for a dynamic integration using refresh token.

        Args:
            db: Database session
            user_id: User ID
            integration_id: Integration definition ID

        Returns:
            Updated credentials if successful, None otherwise
        """
        try:
            # Get current credentials
            credentials = self.retrieve_oauth_credentials_for_integration(
                db, user_id, integration_id
            )

            if not credentials or not credentials.get("tokens", {}).get("refresh_token"):
                logger.warning(
                    f"No refresh token available for user {user_id}, integration {integration_id}"
                )
                return None

            refresh_token = credentials["tokens"]["refresh_token"]

            # Get integration definition
            integration_def = (
                db.query(IntegrationDefinition)
                .filter(IntegrationDefinition.id == integration_id)
                .first()
            )

            if not integration_def:
                logger.error(f"Integration with ID '{integration_id}' not found")
                return None

            # Extract OAuth configuration from the schema
            oauth_schema = integration_def.schema_definition

            # Get OAuth credentials from environment variables using names from schema
            client_id_env_var = oauth_schema.get("client_id_env_var")
            client_secret_env_var = oauth_schema.get("client_secret_env_var")

            if not client_id_env_var or not client_secret_env_var:
                logger.error(
                    f"Missing client credentials configuration for integration {integration_def.name}"
                )
                return None

            # Get actual values from environment variables
            client_id = getattr(settings, client_id_env_var, None)
            client_secret_setting = getattr(settings, client_secret_env_var, None)

            if not client_id or not client_secret_setting:
                logger.error(
                    f"Client credentials not configured in environment for integration {integration_def.name}"
                )
                return None

            # Handle SecretStr type
            if hasattr(client_secret_setting, "get_secret_value"):
                client_secret = client_secret_setting.get_secret_value()
            else:
                client_secret = client_secret_setting

            # Prepare refresh token request
            token_data = {
                "grant_type": "refresh_token",
                "refresh_token": refresh_token,
                "client_id": client_id,
                "client_secret": client_secret,
            }

            # Add scope if available
            if credentials.get("scopes"):
                token_data["scope"] = " ".join(credentials["scopes"])

            headers = {
                "Content-Type": "application/x-www-form-urlencoded",
                "Accept": "application/json",
            }

            # Make token refresh request
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    oauth_schema["token_url"],
                    data=token_data,
                    headers=headers,
                )

                if response.status_code != 200:
                    logger.error(
                        f"Token refresh failed for integration {integration_def.name} with status {response.status_code}: {response.text}"
                    )
                    return None

                token_response = response.json()

                # Validate required token fields
                if "access_token" not in token_response:
                    logger.error(
                        f"Missing access_token in refresh response for integration {integration_def.name}"
                    )
                    return None

                # Update existing tokens with new ones
                updated_tokens = credentials["tokens"].copy()
                updated_tokens["access_token"] = token_response["access_token"]

                # Update refresh token if provided
                if "refresh_token" in token_response:
                    updated_tokens["refresh_token"] = token_response["refresh_token"]

                # Update other token fields
                if "expires_in" in token_response:
                    updated_tokens["expires_in"] = token_response["expires_in"]
                if "token_type" in token_response:
                    updated_tokens["token_type"] = token_response["token_type"]
                if "scope" in token_response:
                    updated_tokens["scope"] = token_response["scope"]

                # Store updated credentials
                scopes = credentials.get("scopes", [])
                if "scope" in token_response:
                    scopes = token_response["scope"].split()

                store_result = self.store_oauth_credentials_for_integration(
                    db, user_id, integration_id, updated_tokens, scopes
                )

                if store_result.get("success"):
                    logger.info(
                        f"Successfully refreshed OAuth token for user {user_id}, integration {integration_id}"
                    )
                    return token_response
                else:
                    logger.error(f"Failed to store refreshed tokens: {store_result.get('message')}")
                    return None

        except httpx.TimeoutException:
            logger.error(f"Timeout during token refresh for integration {integration_id}")
            return None
        except httpx.RequestError as e:
            logger.error(
                f"Request error during token refresh for integration {integration_id}: {e}"
            )
            return None
        except Exception as e:
            logger.error(f"Error refreshing access token for integration: {e}")
            return None


# Global OAuth service instance
oauth_service = OAuthService()
