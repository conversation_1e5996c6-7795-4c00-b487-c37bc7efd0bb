"""
Google Secret Manager Integration

This module provides integration with Google Cloud Secret Manager
for secure storage and retrieval of OAuth tokens and credentials.
"""

import json
import uuid
import base64
import tempfile
import os
from typing import Dict, Optional, Any
from google.cloud import secretmanager
from google.api_core import exceptions as gcp_exceptions
import logging

from app.core.config import settings

logger = logging.getLogger(__name__)


class SecretManager:
    """Google Cloud Secret Manager service for OAuth token storage."""

    def __init__(self):
        """Initialize Secret Manager client."""
        self._client = None
        self._project_id = None
        self._initialized = False

    def _get_project_id(self) -> str:
        """Get project ID from configuration or derive from client ID."""
        # First try to get from GOOGLE_PROJECT_ID if set
        if settings.GOOGLE_PROJECT_ID:
            return settings.GOOGLE_PROJECT_ID

        # If not set, try to derive from GOOGLE_CLIENT_ID
        if settings.GOOGLE_CLIENT_ID:
            # Google OAuth client IDs have format: {project-number}-{random}.apps.googleusercontent.com
            # We'll use a default project ID based on the client ID
            client_id_parts = settings.GOOGLE_CLIENT_ID.split("-")
            if len(client_id_parts) >= 2:
                # Use the project number part as project ID
                return f"project-{client_id_parts[0]}"

        # Fallback to a default project ID
        return "ruh-ai-platform"

    def _load_credentials_from_base64(self):
        """
        Decodes the base64 service account key and sets GOOGLE_APPLICATION_CREDENTIALS.
        """
        creds_b64 = settings.GOOGLE_APPLICATION_CREDENTIALS

        if not creds_b64:
            raise EnvironmentError("GOOGLE_APPLICATION_CREDENTIALS is not set")

        decoded = base64.b64decode(creds_b64)

        # Save to a temporary file
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".json")
        temp_file.write(decoded)
        temp_file.close()

        os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = temp_file.name
        logger.info("Decoded GCP credentials and set GOOGLE_APPLICATION_CREDENTIALS")

    def _ensure_initialized(self):
        """Ensure the Secret Manager client is initialized."""
        if not self._initialized:
            self._project_id = self._get_project_id()
            self._initialize_client()
            self._initialized = True

    def _initialize_client(self):
        """Initialize Google Cloud Secret Manager client."""
        try:
            if not settings.SECRET_MANAGER_ENABLED:
                logger.warning("Secret Manager is disabled in configuration")
                return

            if not self._project_id:
                logger.error("Google Project ID could not be determined")
                raise ValueError("GOOGLE_PROJECT_ID or GOOGLE_CLIENT_ID must be set")

            # Load credentials from base64 if provided
            if settings.GOOGLE_APPLICATION_CREDENTIALS:
                self._load_credentials_from_base64()

            # Initialize the Secret Manager client
            self._client = secretmanager.SecretManagerServiceClient()
            logger.info(
                f"Secret Manager client initialized successfully with project: {self._project_id}"
            )

        except Exception as e:
            logger.error(f"Failed to initialize Secret Manager client: {e}")
            raise

    def store_oauth_tokens(self, user_id: str, tool_name: str, tokens: Dict[str, Any]) -> str:
        """
        Store OAuth tokens in Google Secret Manager.

        Args:
            user_id: User identifier
            tool_name: Tool name
            tokens: OAuth tokens dictionary

        Returns:
            Secret reference ID
        """
        self._ensure_initialized()
        if not self._client:
            raise RuntimeError("Secret Manager client not initialized")

        try:
            # Generate unique secret ID
            secret_id = f"oauth-{user_id}-{tool_name}-{uuid.uuid4().hex[:8]}"

            # Create secret
            parent = f"projects/{self._project_id}"
            secret = {"replication": {"automatic": {}}}

            try:
                created_secret = self._client.create_secret(
                    request={"parent": parent, "secret_id": secret_id, "secret": secret}
                )
                logger.debug(f"Created secret: {created_secret.name}")
            except gcp_exceptions.AlreadyExists:
                logger.debug(f"Secret {secret_id} already exists")

            # Add secret version with token data
            secret_data = json.dumps(tokens).encode("utf-8")

            response = self._client.add_secret_version(
                request={
                    "parent": f"{parent}/secrets/{secret_id}",
                    "payload": {"data": secret_data},
                }
            )

            logger.info(f"Stored OAuth tokens in Secret Manager: {secret_id}")
            return secret_id

        except gcp_exceptions.GoogleAPIError as e:
            logger.error(f"Google API error storing OAuth tokens: {e}")
            raise
        except Exception as e:
            logger.error(f"Error storing OAuth tokens in Secret Manager: {e}")
            raise

    def retrieve_oauth_tokens(self, secret_id: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve OAuth tokens from Google Secret Manager.

        Args:
            secret_id: Secret reference ID

        Returns:
            OAuth tokens dictionary if found, None otherwise
        """
        self._ensure_initialized()
        if not self._client:
            raise RuntimeError("Secret Manager client not initialized")

        try:
            # Build the resource name of the secret version
            name = f"projects/{self._project_id}/secrets/{secret_id}/versions/latest"

            # Access the secret version
            response = self._client.access_secret_version(request={"name": name})

            # Decode and parse the secret data
            secret_data = response.payload.data.decode("utf-8")
            tokens = json.loads(secret_data)

            logger.debug(f"Retrieved OAuth tokens from Secret Manager: {secret_id}")
            return tokens

        except gcp_exceptions.NotFound:
            logger.warning(f"Secret not found: {secret_id}")
            return None
        except gcp_exceptions.GoogleAPIError as e:
            logger.error(f"Google API error retrieving OAuth tokens: {e}")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing OAuth tokens JSON: {e}")
            return None
        except Exception as e:
            logger.error(f"Error retrieving OAuth tokens from Secret Manager: {e}")
            return None

    def update_oauth_tokens(self, secret_id: str, tokens: Dict[str, Any]) -> bool:
        """
        Update OAuth tokens in Google Secret Manager.

        Args:
            secret_id: Secret reference ID
            tokens: Updated OAuth tokens dictionary

        Returns:
            True if successful, False otherwise
        """
        if not self._client:
            raise RuntimeError("Secret Manager client not initialized")

        try:
            # Add new secret version with updated token data
            secret_data = json.dumps(tokens).encode("utf-8")
            parent = f"projects/{self._project_id}/secrets/{secret_id}"

            response = self._client.add_secret_version(
                request={"parent": parent, "payload": {"data": secret_data}}
            )

            logger.info(f"Updated OAuth tokens in Secret Manager: {secret_id}")
            return True

        except gcp_exceptions.GoogleAPIError as e:
            logger.error(f"Google API error updating OAuth tokens: {e}")
            return False
        except Exception as e:
            logger.error(f"Error updating OAuth tokens in Secret Manager: {e}")
            return False

    def delete_oauth_tokens(self, secret_id: str) -> bool:
        """
        Delete OAuth tokens from Google Secret Manager.

        Args:
            secret_id: Secret reference ID

        Returns:
            True if successful, False otherwise
        """
        if not self._client:
            raise RuntimeError("Secret Manager client not initialized")

        try:
            # Delete the secret
            name = f"projects/{self._project_id}/secrets/{secret_id}"

            self._client.delete_secret(request={"name": name})

            logger.info(f"Deleted OAuth tokens from Secret Manager: {secret_id}")
            return True

        except gcp_exceptions.NotFound:
            logger.warning(f"Secret not found for deletion: {secret_id}")
            return True  # Consider it successful if already deleted
        except gcp_exceptions.GoogleAPIError as e:
            logger.error(f"Google API error deleting OAuth tokens: {e}")
            return False
        except Exception as e:
            logger.error(f"Error deleting OAuth tokens from Secret Manager: {e}")
            return False

    def health_check(self) -> bool:
        """
        Perform a health check on the Secret Manager connection.

        Returns:
            True if Secret Manager is accessible, False otherwise
        """
        if not self._client:
            return False

        try:
            # Try to list secrets to test connectivity
            parent = f"projects/{self._project_id}"
            list(self._client.list_secrets(request={"parent": parent}, page_size=1))
            return True
        except Exception as e:
            logger.error(f"Secret Manager health check failed: {e}")
            return False


# Global Secret Manager instance
secret_manager = SecretManager()
