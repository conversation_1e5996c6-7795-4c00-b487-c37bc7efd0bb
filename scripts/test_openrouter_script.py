#!/usr/bin/env python3
"""
Test script to verify the OpenRouter import functionality.
This script tests the data fetching and parsing logic without actually adding to the database.
"""

import requests
import json
from typing import Dict, Any, Optional

def safe_float_conversion(value: Any) -> Optional[float]:
    """Safely convert a value to float."""
    if value is None or value == "0":
        return None
    try:
        return float(value)
    except (ValueError, TypeError):
        return None

def extract_provider_and_model_name(model_id: str) -> tuple[str, str]:
    """
    Extract provider name and model name from OpenRouter model ID.
    Example: "moonshotai/kimi-k2:free" -> ("moonshotai", "kimi-k2:free")
    """
    if "/" in model_id:
        parts = model_id.split("/", 1)
        return parts[0], parts[1]
    else:
        # If no slash, treat the whole thing as model name with unknown provider
        return "unknown", model_id

def test_openrouter_api():
    """Test fetching data from OpenRouter API and parsing it."""
    url = "https://openrouter.ai/api/v1/models"
    
    try:
        print(f"🔄 Testing OpenRouter API: {url}")
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        data = response.json()
        
        if "data" not in data:
            raise ValueError("Invalid response format: missing 'data' field")
        
        models_data = data["data"]
        print(f"✅ Successfully fetched {len(models_data)} models")
        
        # Test parsing a few models
        print(f"\n📋 Testing data parsing with first 5 models:")
        print("-" * 80)
        
        providers_found = set()
        
        for i, item in enumerate(models_data[:5]):
            model_id = item.get("id", "")
            provider_name, model_name = extract_provider_and_model_name(model_id)
            providers_found.add(provider_name)
            
            # Get pricing
            pricing = item.get("pricing", {})
            input_price = safe_float_conversion(pricing.get("prompt"))
            output_price = safe_float_conversion(pricing.get("completion"))
            
            # Get context info
            context_length = item.get("context_length", 4096)
            top_provider = item.get("top_provider", {})
            max_completion_tokens = top_provider.get("max_completion_tokens")
            
            print(f"Model {i+1}:")
            print(f"  ID: {model_id}")
            print(f"  Provider: {provider_name}")
            print(f"  Model: {model_name}")
            print(f"  Context Length: {context_length}")
            print(f"  Max Completion Tokens: {max_completion_tokens}")
            print(f"  Input Price: {input_price}")
            print(f"  Output Price: {output_price}")
            print(f"  Description: {item.get('description', 'N/A')[:100]}...")
            print()
        
        print(f"📊 Summary:")
        print(f"  Total models: {len(models_data)}")
        print(f"  Unique providers found in sample: {len(providers_found)}")
        print(f"  Providers: {', '.join(sorted(providers_found))}")
        
        # Test some edge cases
        print(f"\n🧪 Testing edge cases:")
        
        # Find models with different pricing structures
        free_models = [m for m in models_data if m.get("pricing", {}).get("prompt") == "0"]
        paid_models = [m for m in models_data if m.get("pricing", {}).get("prompt") != "0"]
        
        print(f"  Free models: {len(free_models)}")
        print(f"  Paid models: {len(paid_models)}")
        
        # Find models with different context lengths
        context_lengths = [m.get("context_length", 0) for m in models_data]
        min_context = min(context_lengths)
        max_context = max(context_lengths)
        
        print(f"  Context length range: {min_context} - {max_context}")
        
        return True
        
    except requests.RequestException as e:
        print(f"❌ Failed to fetch data from OpenRouter API: {str(e)}")
        return False
    except json.JSONDecodeError as e:
        print(f"❌ Failed to parse JSON response: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
        return False

def main():
    """Run the test."""
    print("🧪 Testing OpenRouter API Integration")
    print("=" * 60)
    
    success = test_openrouter_api()
    
    if success:
