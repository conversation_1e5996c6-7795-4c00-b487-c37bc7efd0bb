"""
Test cases for API Key Integration functionality.

This module tests the API key integration features including:
- API key credential storage and retrieval
- Schema validation for API key fields
- User-facing API key management operations
"""

import json
import pytest
import uuid
from datetime import datetime, timezone
from unittest.mock import Mock, patch, MagicMock

from sqlalchemy.orm import Session
from app.models.integrations import IntegrationDefinition, ConnectionTypeEnum, OAuthCredential
from app.services.integration_functions import IntegrationService
from app.services.api_key_service import api_key_service
from app.grpc_ import authentication_pb2


class TestAPIKeyIntegration:
    """Test cases for API Key integration functionality."""

    @pytest.fixture
    def mock_db_session(self):
        """Mock database session."""
        return Mock(spec=Session)

    @pytest.fixture
    def integration_service(self):
        """Integration service instance."""
        return IntegrationService()

    @pytest.fixture
    def sample_api_key_integration(self):
        """Sample API key integration definition."""
        return IntegrationDefinition(
            id=str(uuid.uuid4()),
            name="AWS Bedrock",
            description="AWS Bedrock AI service integration",
            connection_type=ConnectionTypeEnum.API_KEY,
            schema_definition=[
                {"name": "api_key", "required": True, "description": "Your API secret key"},
                {"name": "region", "required": False, "description": "AWS Region"}
            ],
            is_active=True,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc)
        )

    @pytest.fixture
    def sample_api_key_credentials(self):
        """Sample API key credentials."""
        return {
            "api_key": "sk-test-api-key-12345",
            "region": "us-east-1"
        }

    def test_create_api_key_integration_definition(self, integration_service, mock_db_session):
        """Test creating an API key integration definition."""
        # Arrange
        request = authentication_pb2.CreateIntegrationRequest(
            admin_user_id="admin-123",
            name="AWS Bedrock",
            description="AWS Bedrock AI service integration",
            connection_type=authentication_pb2.CONNECTION_TYPE_API_KEY,
            schema_definition=json.dumps([
                {"name": "api_key", "required": True, "description": "Your API secret key"},
                {"name": "region", "required": False, "description": "AWS Region"}
            ])
        )

        mock_db_session.query.return_value.filter.return_value.first.return_value = None
        mock_db_session.add = Mock()
        mock_db_session.commit = Mock()

        with patch.object(integration_service, '_get_db_session', return_value=mock_db_session):
            # Act
            response = integration_service.CreateIntegration(request, Mock())

            # Assert
            assert response.success is True
            assert "Integration created successfully" in response.message
            assert response.integration.connection_type == authentication_pb2.CONNECTION_TYPE_API_KEY
            mock_db_session.add.assert_called_once()
            mock_db_session.commit.assert_called_once()

    def test_validate_api_key_schema_definition(self):
        """Test validation of API key schema definition."""
        # Valid schema
        valid_schema = [
            {"name": "api_key", "required": True, "description": "Your API secret key"},
            {"name": "region", "required": False, "description": "AWS Region"}
        ]
        
        # Should not raise any exception
        assert api_key_service.validate_api_key_schema(valid_schema) is True

        # Invalid schema - missing required fields
        invalid_schema = [
            {"name": "api_key", "description": "Your API secret key"}  # missing 'required'
        ]
        
        with pytest.raises(ValueError, match="Missing required field 'required'"):
            api_key_service.validate_api_key_schema(invalid_schema)

        # Invalid schema - invalid field type
        invalid_schema2 = [
            {"name": "api_key", "required": "yes", "description": "Your API secret key"}  # required should be boolean
        ]
        
        with pytest.raises(ValueError, match="Field 'required' must be a boolean"):
            api_key_service.validate_api_key_schema(invalid_schema2)

    def test_store_api_key_credentials(self, mock_db_session, sample_api_key_integration, sample_api_key_credentials):
        """Test storing API key credentials."""
        # Arrange
        user_id = "user-123"
        integration_id = sample_api_key_integration.id
        
        # Mock database queries
        mock_db_session.query.return_value.filter.return_value.first.side_effect = [
            sample_api_key_integration,  # First call for integration lookup
            None  # Second call for existing credential check
        ]
        mock_db_session.add = Mock()
        mock_db_session.commit = Mock()

        with patch('app.services.api_key_service.EncryptionManager') as mock_encryption_manager_class:
            mock_encryption_manager = Mock()
            mock_encryption_manager_class.return_value = mock_encryption_manager
            
            # Mock encryption manager methods
            mock_encryption_manager.get_user_encryption_key.side_effect = ValueError("No key found")
            mock_encryption_manager.create_and_store_user_key.return_value = "encryption-key"
            mock_encryption_manager.encrypt.return_value = "encrypted-credentials"
            mock_encryption_manager.project_id = "test-project"
            mock_encryption_manager.secret_client.add_secret_version.return_value = None
            
            # Create a new service instance to use the mocked encryption manager
            from app.services.api_key_service import APIKeyService
            test_service = APIKeyService()
            
            # Act
            result = test_service.store_api_key_credentials(
                mock_db_session, user_id, integration_id, sample_api_key_credentials
            )

            # Assert
            assert result["success"] is True
            assert "API key credentials stored successfully" in result["message"]
            mock_encryption_manager.encrypt.assert_called_once()
            mock_db_session.add.assert_called_once()
            mock_db_session.commit.assert_called_once()

    def test_retrieve_api_key_credentials(self, mock_db_session, sample_api_key_integration, sample_api_key_credentials):
        """Test retrieving API key credentials."""
        # Arrange
        user_id = "user-123"
        integration_id = sample_api_key_integration.id
        
        mock_credential = OAuthCredential(
            id=str(uuid.uuid4()),
            user_id=user_id,
            integration_definition_id=integration_id,
            secret_reference="secret-ref-123",
            is_connected=True,
            created_at=datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
            last_used_at=datetime.now(timezone.utc)
        )
        
        mock_db_session.query.return_value.filter.return_value.first.return_value = mock_credential
        mock_db_session.commit = Mock()

        with patch('app.services.api_key_service.EncryptionManager') as mock_encryption_manager_class:
            mock_encryption_manager = Mock()
            mock_encryption_manager_class.return_value = mock_encryption_manager
            
            # Mock encryption manager methods
            mock_encryption_manager.project_id = "test-project"
            mock_response = Mock()
            mock_response.payload.data.decode.return_value = "encrypted-credentials"
            mock_encryption_manager.secret_client.access_secret_version.return_value = mock_response
            mock_encryption_manager.decrypt.return_value = json.dumps(sample_api_key_credentials)
            
            # Create a new service instance to use the mocked encryption manager
            from app.services.api_key_service import APIKeyService
            test_service = APIKeyService()
            
            # Act
            result = test_service.retrieve_api_key_credentials(mock_db_session, user_id, integration_id)

            # Assert
            assert result is not None
            assert result["user_id"] == user_id
            assert result["integration_id"] == integration_id
            assert result["credentials"] == sample_api_key_credentials
            mock_encryption_manager.decrypt.assert_called_once()

    def test_delete_api_key_credentials(self, mock_db_session):
        """Test deleting API key credentials."""
        # Arrange
        user_id = "user-123"
        integration_id = "integration-123"
        
        mock_credential = OAuthCredential(
            id=str(uuid.uuid4()),
            user_id=user_id,
            integration_definition_id=integration_id,
            secret_reference="secret-ref-123",
            is_connected=True
        )
        
        mock_db_session.query.return_value.filter.return_value.first.return_value = mock_credential
        mock_db_session.delete = Mock()
        mock_db_session.commit = Mock()

        with patch('app.services.api_key_service.EncryptionManager') as mock_encryption_manager_class:
            mock_encryption_manager = Mock()
            mock_encryption_manager_class.return_value = mock_encryption_manager
            
            # Mock encryption manager methods
            mock_encryption_manager.project_id = "test-project"
            mock_encryption_manager.secret_client.delete_secret.return_value = None
            
            # Create a new service instance to use the mocked encryption manager
            from app.services.api_key_service import APIKeyService
            test_service = APIKeyService()
            
            # Act
            result = test_service.delete_api_key_credentials(mock_db_session, user_id, integration_id)

            # Assert
            assert result is True
            mock_encryption_manager.secret_client.delete_secret.assert_called_once()
            mock_db_session.delete.assert_called_once_with(mock_credential)
            mock_db_session.commit.assert_called_once()

    def test_validate_api_key_credentials_against_schema(self, sample_api_key_integration):
        """Test validating API key credentials against schema definition."""
        # Valid credentials
        valid_credentials = {
            "api_key": "sk-test-api-key-12345",
            "region": "us-east-1"
        }
        
        result = api_key_service.validate_credentials_against_schema(
            valid_credentials, sample_api_key_integration.schema_definition
        )
        assert result["valid"] is True

        # Missing required field
        invalid_credentials = {
            "region": "us-east-1"  # missing required api_key
        }
        
        result = api_key_service.validate_credentials_against_schema(
            invalid_credentials, sample_api_key_integration.schema_definition
        )
        assert result["valid"] is False
        assert "Required field 'api_key' is missing" in result["errors"]

        # Extra fields should be allowed
        credentials_with_extra = {
            "api_key": "sk-test-api-key-12345",
            "region": "us-east-1",
            "extra_field": "extra_value"
        }
        
        result = api_key_service.validate_credentials_against_schema(
            credentials_with_extra, sample_api_key_integration.schema_definition
        )
        assert result["valid"] is True

    def test_list_user_api_key_integrations(self, mock_db_session):
        """Test listing user's API key integrations."""
        # Arrange
        user_id = "user-123"
        
        mock_credentials = [
            OAuthCredential(
                id=str(uuid.uuid4()),
                user_id=user_id,
                integration_definition_id="integration-1",
                is_connected=True,
                last_used_at=datetime.now(timezone.utc),
                created_at=datetime.now(timezone.utc)
            ),
            OAuthCredential(
                id=str(uuid.uuid4()),
                user_id=user_id,
                integration_definition_id="integration-2",
                is_connected=False,
                last_used_at=datetime.now(timezone.utc),
                created_at=datetime.now(timezone.utc)
            )
        ]
        
        mock_integrations = [
            IntegrationDefinition(
                id="integration-1",
                name="AWS Bedrock",
                description="AWS Bedrock AI service",
                connection_type=ConnectionTypeEnum.API_KEY,
                is_active=True
            ),
            IntegrationDefinition(
                id="integration-2",
                name="OpenAI",
                description="OpenAI API service",
                connection_type=ConnectionTypeEnum.API_KEY,
                is_active=True
            )
        ]
        
        # Mock the database queries
        mock_db_session.query.return_value.filter.return_value.all.return_value = mock_credentials
        mock_db_session.query.return_value.filter.return_value.first.side_effect = mock_integrations

        # Act
        result = api_key_service.list_user_api_key_integrations(mock_db_session, user_id)

        # Assert
        assert len(result) == 2
        assert result[0]["integration_name"] == "AWS Bedrock"
        assert result[0]["is_connected"] is True
        assert result[1]["integration_name"] == "OpenAI"
        assert result[1]["is_connected"] is False
