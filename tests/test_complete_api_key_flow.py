"""
Complete API Key Integration Flow Test

This module tests the complete end-to-end flow for API key integrations:
1. <PERSON>min creates an API key integration definition
2. User stores API key credentials
3. User retrieves API key credentials
4. User updates API key credentials
5. User deletes API key credentials
"""

import json
import pytest
import uuid
from datetime import datetime, timezone
from unittest.mock import Mock, patch

from app.services.integration_functions import IntegrationService
from app.grpc_ import authentication_pb2
from app.models.integrations import IntegrationDefinition, ConnectionTypeEnum


class TestCompleteAPIKeyFlow:
    """Test complete API key integration flow."""

    @pytest.fixture
    def integration_service(self):
        """Integration service instance."""
        return IntegrationService()

    @pytest.fixture
    def aws_bedrock_schema(self):
        """AWS Bedrock API key schema."""
        return [
            {"name": "api_key", "required": True, "description": "Your AWS API secret key"},
            {"name": "region", "required": False, "description": "AWS Region (default: us-east-1)"},
            {"name": "endpoint", "required": False, "description": "Custom endpoint URL"}
        ]

    @pytest.fixture
    def user_credentials(self):
        """Sample user credentials."""
        return {
            "api_key": "AKIAIOSFODNN7EXAMPLE",
            "region": "us-west-2",
            "endpoint": "https://bedrock-runtime.us-west-2.amazonaws.com"
        }

    def test_complete_api_key_integration_flow(self, integration_service, aws_bedrock_schema, user_credentials):
        """Test the complete API key integration flow."""
        
        # Step 1: Admin creates an API key integration definition
        print("\n=== Step 1: Admin creates API key integration ===")
        
        create_request = authentication_pb2.CreateIntegrationRequest(
            admin_user_id="admin-123",
            name="AWS Bedrock",
            description="AWS Bedrock AI service for generative AI applications",
            connection_type=authentication_pb2.CONNECTION_TYPE_API_KEY,
            schema_definition=json.dumps(aws_bedrock_schema)
        )

        mock_context = Mock()
        integration_id = None

        with patch.object(integration_service, '_get_db_session') as mock_get_db:
            mock_db = Mock()
            mock_get_db.return_value = mock_db
            mock_db.query.return_value.filter.return_value.first.return_value = None
            mock_db.add = Mock()
            mock_db.commit = Mock()

            # Create integration
            create_response = integration_service.CreateIntegration(create_request, mock_context)
            
            assert create_response.success is True
            assert "Integration created successfully" in create_response.message
            assert create_response.integration.name == "AWS Bedrock"
            assert create_response.integration.connection_type == authentication_pb2.CONNECTION_TYPE_API_KEY
            
            integration_id = create_response.integration.id
            print(f"✓ Integration created with ID: {integration_id}")

        # Step 2: User stores API key credentials
        print("\n=== Step 2: User stores API key credentials ===")
        
        store_request = authentication_pb2.StoreAPIKeyCredentialsRequest(
            user_id="user-456",
            integration_id=integration_id,
            credentials=json.dumps(user_credentials)
        )

        with patch.object(integration_service, '_get_db_session') as mock_get_db:
            mock_db = Mock()
            mock_get_db.return_value = mock_db
            
            with patch('app.services.integration_functions.api_key_service') as mock_api_key_service:
                mock_api_key_service.store_api_key_credentials.return_value = {
                    "success": True,
                    "message": "API key credentials stored successfully",
                    "integration_id": integration_id
                }

                store_response = integration_service.StoreAPIKeyCredentials(store_request, mock_context)
                
                assert store_response.success is True
                assert "API key credentials stored successfully" in store_response.message
                assert store_response.integration_id == integration_id
                print("✓ API key credentials stored successfully")

        # Step 3: User retrieves API key credentials
        print("\n=== Step 3: User retrieves API key credentials ===")
        
        get_request = authentication_pb2.GetAPIKeyCredentialsRequest(
            user_id="user-456",
            integration_id=integration_id
        )

        with patch.object(integration_service, '_get_db_session') as mock_get_db:
            mock_db = Mock()
            mock_get_db.return_value = mock_db
            
            with patch('app.services.integration_functions.api_key_service') as mock_api_key_service:
                mock_api_key_service.retrieve_api_key_credentials.return_value = {
                    "user_id": "user-456",
                    "integration_id": integration_id,
                    "credentials": user_credentials,
                    "is_connected": True,
                    "last_used_at": "2023-01-01T00:00:00Z"
                }

                get_response = integration_service.GetAPIKeyCredentials(get_request, mock_context)
                
                assert get_response.success is True
                assert "API key credentials retrieved successfully" in get_response.message
                assert get_response.user_id == "user-456"
                assert get_response.integration_id == integration_id
                assert json.loads(get_response.credentials) == user_credentials
                assert get_response.is_connected is True
                print("✓ API key credentials retrieved successfully")

        # Step 4: User updates API key credentials
        print("\n=== Step 4: User updates API key credentials ===")
        
        updated_credentials = {
            "api_key": "AKIAIOSFODNN7UPDATED",
            "region": "eu-west-1",
            "endpoint": "https://bedrock-runtime.eu-west-1.amazonaws.com"
        }
        
        update_request = authentication_pb2.UpdateAPIKeyCredentialsRequest(
            user_id="user-456",
            integration_id=integration_id,
            credentials=json.dumps(updated_credentials)
        )

        with patch.object(integration_service, '_get_db_session') as mock_get_db:
            mock_db = Mock()
            mock_get_db.return_value = mock_db
            
            with patch('app.services.integration_functions.api_key_service') as mock_api_key_service:
                mock_api_key_service.update_api_key_credentials.return_value = {
                    "success": True,
                    "message": "API key credentials updated successfully",
                    "integration_id": integration_id
                }

                update_response = integration_service.UpdateAPIKeyCredentials(update_request, mock_context)
                
                assert update_response.success is True
                assert "API key credentials updated successfully" in update_response.message
                assert update_response.integration_id == integration_id
                print("✓ API key credentials updated successfully")

        # Step 5: User deletes API key credentials
        print("\n=== Step 5: User deletes API key credentials ===")
        
        delete_request = authentication_pb2.DeleteAPIKeyCredentialsRequest(
            user_id="user-456",
            integration_id=integration_id
        )

        with patch.object(integration_service, '_get_db_session') as mock_get_db:
            mock_db = Mock()
            mock_get_db.return_value = mock_db
            
            with patch('app.services.integration_functions.api_key_service') as mock_api_key_service:
                mock_api_key_service.delete_api_key_credentials.return_value = True

                delete_response = integration_service.DeleteAPIKeyCredentials(delete_request, mock_context)
                
                assert delete_response.success is True
                assert "API key credentials deleted successfully" in delete_response.message
                print("✓ API key credentials deleted successfully")

        print("\n=== Complete API Key Integration Flow Test PASSED ===")
        print("✓ All steps completed successfully:")
        print("  1. Admin created API key integration definition")
        print("  2. User stored API key credentials")
        print("  3. User retrieved API key credentials")
        print("  4. User updated API key credentials")
        print("  5. User deleted API key credentials")

    def test_api_key_validation_in_flow(self, integration_service):
        """Test API key validation during the complete flow."""
        
        # Test with invalid schema
        invalid_schema = [
            {"name": "api_key", "description": "Missing required field"}  # missing 'required'
        ]
        
        create_request = authentication_pb2.CreateIntegrationRequest(
            admin_user_id="admin-123",
            name="Invalid Integration",
            description="Integration with invalid schema",
            connection_type=authentication_pb2.CONNECTION_TYPE_API_KEY,
            schema_definition=json.dumps(invalid_schema)
        )

        mock_context = Mock()
        create_response = integration_service.CreateIntegration(create_request, mock_context)
        
        assert create_response.success is False
        assert "Invalid API key schema" in create_response.message
        assert "Missing required field 'required'" in create_response.message
        print("✓ Schema validation works correctly during integration creation")

    def test_credential_validation_in_flow(self, integration_service):
        """Test credential validation during storage."""
        
        # Valid schema
        schema = [
            {"name": "api_key", "required": True, "description": "API key"},
            {"name": "region", "required": False, "description": "Region"}
        ]
        
        # Invalid credentials (missing required field)
        invalid_credentials = {
            "region": "us-east-1"  # missing required api_key
        }
        
        store_request = authentication_pb2.StoreAPIKeyCredentialsRequest(
            user_id="user-456",
            integration_id="integration-123",
            credentials=json.dumps(invalid_credentials)
        )

        mock_context = Mock()

        with patch.object(integration_service, '_get_db_session') as mock_get_db:
            mock_db = Mock()
            mock_get_db.return_value = mock_db
            
            with patch('app.services.integration_functions.api_key_service') as mock_api_key_service:
                mock_api_key_service.store_api_key_credentials.return_value = {
                    "success": False,
                    "message": "Credential validation failed",
                    "errors": ["Required field 'api_key' is missing"]
                }

                store_response = integration_service.StoreAPIKeyCredentials(store_request, mock_context)
                
                assert store_response.success is False
                assert "Credential validation failed" in store_response.message
                print("✓ Credential validation works correctly during storage")