"""
Test API Key Service Refactor
Tests to ensure no regression when refactoring API key service to use single user encryption key.
"""

import json
import pytest
import uuid
from datetime import datetime, timezone
from unittest.mock import Mock, patch, MagicMock

from app.services.api_key_service import APIKeyService
from app.models.integrations import IntegrationDefinition, ConnectionTypeEnum, OAuthCredential


class TestAPIKeyServiceRefactor:
    """Test API key service functionality before and after refactor."""

    @pytest.fixture
    def api_key_service(self):
        """API key service instance."""
        return APIKeyService()

    @pytest.fixture
    def mock_db(self):
        """Mock database session."""
        return Mock()

    @pytest.fixture
    def sample_integration(self):
        """Sample integration definition."""
        integration = Mock(spec=IntegrationDefinition)
        integration.id = "integration-123"
        integration.name = "Test API Integration"
        integration.connection_type = ConnectionTypeEnum.API_KEY
        integration.schema_definition = [
            {"name": "api_key", "required": True, "description": "API key"},
            {"name": "region", "required": False, "description": "Region"}
        ]
        return integration

    @pytest.fixture
    def sample_credentials(self):
        """Sample user credentials."""
        return {
            "api_key": "test-api-key-123",
            "region": "us-east-1"
        }

    @pytest.fixture
    def sample_oauth_credential(self):
        """Sample OAuth credential record."""
        credential = Mock(spec=OAuthCredential)
        credential.id = "credential-123"
        credential.user_id = "user-456"
        credential.integration_definition_id = "integration-123"
        credential.secret_reference = "test-secret-ref"
        credential.is_connected = True
        credential.last_used_at = datetime.now(timezone.utc)
        credential.created_at = datetime.now(timezone.utc)
        credential.updated_at = datetime.now(timezone.utc)
        return credential

    def test_store_api_key_credentials_success(self, api_key_service, mock_db, sample_integration, sample_credentials):
        """Test successful storage of API key credentials."""
        # Setup mocks
        mock_db.query.return_value.filter.return_value.first.side_effect = [
            sample_integration,  # Integration exists
            None  # No existing credential
        ]
        mock_db.add = Mock()
        mock_db.commit = Mock()

        with patch.object(api_key_service.encryption_manager, 'get_user_encryption_key') as mock_get_key, \
             patch.object(api_key_service.encryption_manager, 'create_and_store_user_key') as mock_create_key, \
             patch.object(api_key_service.encryption_manager, 'encrypt') as mock_encrypt, \
             patch.object(api_key_service.encryption_manager, 'secret_client') as mock_secret_client:
            
            mock_get_key.side_effect = ValueError("No key found")  # Trigger key creation
            mock_create_key.return_value = "test-key"
            mock_encrypt.return_value = "encrypted-credentials"
            mock_secret_client.add_secret_version = Mock()

            result = api_key_service.store_api_key_credentials(
                mock_db, "user-456", "integration-123", sample_credentials
            )

            assert result["success"] is True
            assert "API key credentials stored successfully" in result["message"]
            assert result["integration_id"] == "integration-123"
            mock_db.add.assert_called_once()
            mock_db.commit.assert_called_once()

    def test_retrieve_api_key_credentials_success(self, api_key_service, mock_db, sample_oauth_credential, sample_credentials):
        """Test successful retrieval of API key credentials."""
        # Setup mocks
        mock_db.query.return_value.filter.return_value.first.return_value = sample_oauth_credential

        with patch.object(api_key_service.encryption_manager, 'secret_client') as mock_secret_client, \
             patch.object(api_key_service.encryption_manager, 'decrypt') as mock_decrypt:
            
            mock_response = Mock()
            mock_response.payload.data.decode.return_value = "encrypted-credentials"
            mock_secret_client.access_secret_version.return_value = mock_response
            mock_decrypt.return_value = json.dumps(sample_credentials)

            result = api_key_service.retrieve_api_key_credentials(
                mock_db, "user-456", "integration-123"
            )

            assert result is not None
            assert result["user_id"] == "user-456"
            assert result["integration_id"] == "integration-123"
            assert result["credentials"] == sample_credentials
            assert result["is_connected"] is True
            mock_db.commit.assert_called_once()  # For updating last_used_at

    def test_update_api_key_credentials_success(self, api_key_service, mock_db, sample_integration, sample_oauth_credential, sample_credentials):
        """Test successful update of API key credentials."""
        updated_credentials = {
            "api_key": "updated-api-key-456",
            "region": "us-west-2"
        }

        # Setup mocks
        mock_db.query.return_value.filter.return_value.first.side_effect = [
            sample_oauth_credential,  # Existing credential
            sample_integration  # Integration exists
        ]

        with patch.object(api_key_service.encryption_manager, 'encrypt') as mock_encrypt, \
             patch.object(api_key_service.encryption_manager, 'secret_client') as mock_secret_client:
            
            mock_encrypt.return_value = "encrypted-updated-credentials"
            mock_secret_client.add_secret_version = Mock()

            result = api_key_service.update_api_key_credentials(
                mock_db, "user-456", "integration-123", updated_credentials
            )

            assert result["success"] is True
            assert "API key credentials updated successfully" in result["message"]
            assert result["integration_id"] == "integration-123"
            mock_db.commit.assert_called_once()

    def test_delete_api_key_credentials_success(self, api_key_service, mock_db, sample_oauth_credential):
        """Test successful deletion of API key credentials."""
        # Setup mocks
        mock_db.query.return_value.filter.return_value.first.return_value = sample_oauth_credential
        mock_db.delete = Mock()
        mock_db.commit = Mock()

        result = api_key_service.delete_api_key_credentials(
            mock_db, "user-456", "integration-123"
        )

        assert result is True
        mock_db.delete.assert_called_once_with(sample_oauth_credential)
        mock_db.commit.assert_called_once()
        # No GSM operations in the new approach - only database deletion

    def test_credential_validation_against_schema(self, api_key_service):
        """Test credential validation against schema."""
        schema = [
            {"name": "api_key", "required": True, "description": "API key"},
            {"name": "region", "required": False, "description": "Region"}
        ]

        # Valid credentials
        valid_credentials = {"api_key": "test-key", "region": "us-east-1"}
        result = api_key_service.validate_credentials_against_schema(valid_credentials, schema)
        assert result["valid"] is True
        assert len(result["errors"]) == 0

        # Invalid credentials (missing required field)
        invalid_credentials = {"region": "us-east-1"}
        result = api_key_service.validate_credentials_against_schema(invalid_credentials, schema)
        assert result["valid"] is False
        assert "Required field 'api_key' is missing" in result["errors"]

    def test_api_key_schema_validation(self, api_key_service):
        """Test API key schema validation."""
        # Valid schema
        valid_schema = [
            {"name": "api_key", "required": True, "description": "API key"},
            {"name": "region", "required": False, "description": "Region"}
        ]
        assert api_key_service.validate_api_key_schema(valid_schema) is True

        # Invalid schema (missing required field)
        invalid_schema = [
            {"name": "api_key", "description": "API key"}  # missing 'required'
        ]
        with pytest.raises(ValueError, match="Missing required field 'required'"):
            api_key_service.validate_api_key_schema(invalid_schema)

    def test_list_user_api_key_integrations(self, api_key_service, mock_db):
        """Test listing user's API key integrations."""
        # Setup mock credentials and integrations
        mock_credential = Mock(spec=OAuthCredential)
        mock_credential.integration_definition_id = "integration-123"
        mock_credential.is_connected = True
        mock_credential.last_used_at = datetime.now(timezone.utc)
        mock_credential.created_at = datetime.now(timezone.utc)

        mock_integration = Mock(spec=IntegrationDefinition)
        mock_integration.id = "integration-123"
        mock_integration.name = "Test Integration"
        mock_integration.description = "Test Description"

        mock_db.query.return_value.filter.return_value.all.return_value = [mock_credential]
        mock_db.query.return_value.filter.return_value.first.return_value = mock_integration

        result = api_key_service.list_user_api_key_integrations(mock_db, "user-456")

        assert len(result) == 1
        assert result[0]["integration_id"] == "integration-123"
        assert result[0]["integration_name"] == "Test Integration"
        assert result[0]["is_connected"] is True

    def test_error_handling_integration_not_found(self, api_key_service, mock_db, sample_credentials):
        """Test error handling when integration is not found."""
        mock_db.query.return_value.filter.return_value.first.return_value = None

        result = api_key_service.store_api_key_credentials(
            mock_db, "user-456", "nonexistent-integration", sample_credentials
        )

        assert result["success"] is False
        assert "Integration not found" in result["message"]

    def test_error_handling_credential_not_found(self, api_key_service, mock_db):
        """Test error handling when credential is not found."""
        mock_db.query.return_value.filter.return_value.first.return_value = None

        result = api_key_service.retrieve_api_key_credentials(
            mock_db, "user-456", "integration-123"
        )

        assert result is None

    def test_error_handling_wrong_connection_type(self, api_key_service, mock_db, sample_credentials):
        """Test error handling when integration is not API key type."""
        mock_integration = Mock(spec=IntegrationDefinition)
        mock_integration.connection_type = ConnectionTypeEnum.OAUTH
        mock_db.query.return_value.filter.return_value.first.return_value = mock_integration

        result = api_key_service.store_api_key_credentials(
            mock_db, "user-456", "integration-123", sample_credentials
        )

        assert result["success"] is False
        assert "Integration is not an API key type" in result["message"]