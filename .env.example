# Application Settings
ENV=
APP_NAME=mcp-service
DEBUG=false
PORT=50059

# Database Settings
DB_HOST=localhost
DB_PORT=5432
DB_USER=user_service
DB_PASSWORD=userpass
DB_NAME=user_db

# Slack OAuth
SLACK_CLIENT_ID=
SLACK_CLIENT_SECRET=
SLACK_REDIRECT_URI=

# Redis Settings
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# Proto
REPO_URL=
GIT_TOKEN=

# Security Settings
SERVER_AUTH_KEY=your-server-auth-key
JWT_SECRET_KEY=your-jwt-secret-key


# OAuth Provider Configurations

# Google OAuth
GOOGLE_CLIENT_ID="google-client-id"
GOOGLE_CLIENT_SECRET="google-client-secret"
GOOGLE_REDIRECT_URI="http://localhost:8000/api/v1/oauth/callback"

# Zoho OAuth
ZOHO_CLIENT_ID="zoho-client-id"
ZOHO_CLIENT_SECRET="zoho-client-secret"
ZOHO_REDIRECT_URI="http://localhost:8000/api/v1/oauth/callback"

# Microsoft OAuth
MICROSOFT_CLIENT_ID="microsoft-client-id"
MICROSOFT_CLIENT_SECRET="microsoft-client-secret"
MICROSOFT_REDIRECT_URI="http://localhost:8000/api/v1/oauth/callback"

# GitHub OAuth
GITHUB_CLIENT_ID="github-client-id"
GITHUB_CLIENT_SECRET="github-client-secret"
GITHUB_REDIRECT_URI="http://localhost:8000/api/v1/oauth/callback"

# Jira OAuth
JIRA_CLIENT_ID="jira-client-id"
JIRA_CLIENT_SECRET="jira-client-secret"
JIRA_REDIRECT_URI="http://localhost:8000/api/v1/oauth/callback"